<template>
	<view v-if="template">
		<s-layout title="首页" navbar="custom" tabbar="/pages/index/index" :bgStyle="template.style?.background"
			:navbarStyle="template.style?.navbar" onShareAppMessage :showFloatButton="true" bgColor="#fcc74e">
			<view class="container">
				<!-- 顶部 -->
				<view class="top">
					<!-- 顶部-地址&搜索 -->
					<view class="locTop">
						<view class="locTop-left" @click="state.showRegion = true">
							<image style="width: 34rpx;height: 34rpx;"
								src="https://jiangxiaoxian.0rui.cn/locationTop.png" mode=""></image>
							<view 
							style="margin-left: 10rpx;font-size: 30rpx;font-weight: 800;line-height: 45rpx;
							white-space: nowrap;overflow: hidden;text-overflow: ellipsis;width: 270rpx;">
								{{state.model.city_name ? proCity:'全国' }}
							</view>
						</view>
						<view class="locTop-right" @click="toPage('/packageA/search/index')">
							<uni-section style="border-radius: 192rpx;padding: 0;" type="line">
								<uni-search-bar style="border-radius: 192rpx;" radius="23" placeholder="搜索您需要的信息"
									bgColor="#ffffff" clearButton="none" cancelButton="none"
									:readonly="true" />
							</uni-section>
							<!-- @confirm="search"-->
						</view>
					</view>

					<!-- 顶部-三个内容跳转 -->
					<view class="navTop">
						<view class="navTop-left" @click="toPage('/pages/canteenRent/rentList')">
							<view class="title" style="">食堂招租</view>
							<view class="intro" style="color: #f42b2b;margin-top: 20rpx;">味揽商机 食载未来</view>
						</view>
						<view class="navTop-right">
							<view class="navRight1" @click="toPage('/pages/school/schoolIndex')">
								<view class="title" style="">平台学院</view>
								<view class="intro" style="color: #05b0b0;margin-top: 20rpx;">学知赋能成长</view>
							</view>
							<view class="navRight2" @click="toPage('/pages/index/category')">
								<view class="title" style="">原料商城</view>
								<view class="intro" style="color: #f58f3d;margin-top: 20rpx;">优材好料直供</view>
							</view>
						</view>
					</view>

				</view>
				<!-- 轮播图 -->
				<view class="swiper-box">
					<view class="swiper_s">
						<swiper class="swiper_s" :circular="true" :autoplay="true" indicator-active-color="#0DAE11"
							indicator-color="#ffffff" :indicator-dots="false" :current="swiperCurrent"
							@animationfinish="swiperChange">
							<!-- v-for="(item, index) in swiperList" :key="index" @click="openSwiper(item)"-->
							<swiper-item style="margin: 0 auto;"  v-for="(item, index) in bannerList" :key="index" @click="swiperJump(item)">
								<view style="position: relative;width: 100%;height: 100%;">
									<!--<view class="fnon_tit"></view>
									 <view class="text">{{ item.title ? item.title : '' }}</view> 
									 @click="click(item.url)" 跳转活动链接 -->
									<image class="swiper-image" :src="item.image" mode="scaleToFill" />
								</view>
							</swiper-item>
							<!-- <swiper-item style="margin: 0 auto;">
								<view style="position: relative;width: 100%;height: 100%">
									<!--<view class="fnon_tit"></view>
									 <view class="text">{{ item.title ? item.title : '' }}</view> 
									 @click="click(item.url)" 跳转活动链接 
									<image class="swiper-image" src="/static/cart-empty.png" mode="scaleToFill" />
								</view>
							</swiper-item> -->
						</swiper>
					</view>
				</view>
				<!-- 推荐列表 -->
				<view class="recommend-box">
					<!-- 推荐标题 -->
					<view class="recom-titleBox">
						<view style="font-size: 38rpx;line-height: 38rpx;font-weight: 800;color: #333333;">平台推荐</view>
						<view class="title-more" @click="toPage('/pages/canteenRent/rentList')">
							<view style="font-size: 26rpx;line-height: 26rpx;font-weight: 400;color: #999999;">查看更多
							</view>
							<image style="width: 28rpx;height: 28rpx;margin-left: 20rpx;"
								src="https://jiangxiaoxian.0rui.cn/nextMore.png" mode=""></image>
						</view>
					</view>
					<view class="rrecommList" v-for="(item,index) in rentList" :key="index" @click="toPage(`/pages/canteenRent/rentDetail?id=` + item.id)">
						<view class="reListItem">
							<!-- 左侧图片 -->
							<view class="listItem-images">
								<image v-if="item.recommend == 1"
									style="width: 77rpx;height: 36rpx;z-index: 100;position: relative;top: 34rpx;left: 0rpx;"
									src="https://jiangxiaoxian.0rui.cn/hot.png"></image>
								<image class="iamges-mainImg" :src="item.images[0]" mode="aspectFill"></image>
							</view>
							<!-- 右侧内容 -->
							<view class="listItem-contents">
								<view class="title2" style="margin-bottom: 20rpx;">{{item.platform_title}}</view>
								<scroll-view class="score-box">
									<view class="fs11 flex cate">
										<view class="cate-e" v-for="(e,i) in item.cate" :key="i">
											<view v-show="e" class="status-tag " style="margin-right: 20rpx;">{{e}}
											</view>
										</view>

										<!-- <text v-show="item.cate[1]"
											class="status-tag" style="margin-right: 20rpx;">{{item.cate[1]}}</text>
										<text v-show="item.cate[2]"
											class="status-tag">{{item.cate[2]}}</text> -->
									</view>
								</scroll-view>
								<view class="cons-third">
									<view style="display: flex;">
										<image style="width: 24rpx;height: 24rpx;"
											src="https://jiangxiaoxian.0rui.cn/locationList.png"></image>
										<view class="title3" style="margin-left: 10rpx;">{{item.address_city_text}}
										</view>
									</view>
									<view class="title3">{{item.release_time_text}}</view>
									<view style="display: flex;">
										<image style="width: 26rpx;height: 20rpx;"
											src="https://jiangxiaoxian.0rui.cn/eye.png"></image>
										<view class="title3" style="margin-left: 10rpx;">{{item.views}}</view>
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>

			</view>


			<!-- 省市区弹窗 -->
			<su-regionCity-picker :show="state.showRegion" @cancel="state.showRegion = false"
				@confirm="onRegionConfirm">
			</su-regionCity-picker>

		</s-layout>
	</view>
</template>

<script setup>
	import {
		computed,
		ref,
		reactive,
	} from 'vue';
	import {
		onLoad,
		onPageScroll,
		onPullDownRefresh
	} from '@dcloudio/uni-app';
	import sheep from '@/sheep';
	import $share from '@/sheep/platform/share';
	import {
		consignee,
		mobile,
		address,
		region
	} from '@/sheep/validate/form';

	const swiperCurrent=ref(0);
	function getAreaCity() {
	  // if (_.isEmpty(uni.getStorageSync('areaCity'))) {
	    sheep.$api.rent.getCity().then((res) => {
	      if (res.code === 1) {
	        uni.setStorageSync('areaCity', res.data);
	      }
	    });
	  // }
	};
	
	function getTag() {
	  // if (_.isEmpty(uni.getStorageSync('areaCity'))) {
	    sheep.$api.rent.tagsList().then((res) => {
	      if (res.code === 1) {
	        uni.setStorageSync('tagList', res.data.list);
	      }
	    });
	  // }
	};
	
	function getType() {
	  // if (_.isEmpty(uni.getStorageSync('areaCity'))) {
	    sheep.$api.rent.listType().then((res) => {
	      if (res.code === 1) {
	        uni.setStorageSync('typeList', res.data);
	      }
	    });
	  // }
	};
	

	const template = computed(() => sheep.$store('app').template?.home);
	const isLogin = computed(() => sheep.$store('user').isLogin);
	const qualification = ref(false)

	const state = reactive({
		showRegion: false,
		model: {
			consignee: '',
			mobile: '',
			address: '',
			is_default: false,
			region: '',
		},
		rules: {
			consignee,
			mobile,
			address,
			region,
		},
	});
	
	const getAreaData = () => {
	  if (_.isEmpty(uni.getStorageSync('areaData'))) {
	    sheep.$api.data.area().then((res) => {
	      if (res.code === 1) {
	        uni.setStorageSync('areaData', res.data);
	      }
	    });
	  }
	};
	
	

	const proCity = ref('')

	const onRegionConfirm = (e) => {
		console.log('onRegionConfirm', e);

		state.model = {
			...state.model,
			...e,
		};
		
		if(state.model.province_name == state.model.city_name) {
			proCity.value = state.model.province_name
		}else {
			// proCity.value = state.model.province_name + " " + state.model.city_name
			proCity.value = state.model.province_name + state.model.city_name
		}

		// proCity.value = state.model.province_name + " " + state.model.city_name

		console.log('onRegionConfirm33', state.model, proCity.value);
		listQuery.value.province = state.model.province_id
		listQuery.value.city = state.model.city_id
		getList()
		state.showRegion = false;
	};

	const listQuery = ref({
		page: 1,
		limit: 10,
		addrLatitude: null,
		addrLongitude: null,
		address: null,
		address1: null,
		categoryId: null,
		tags: '',
		cate_ids: '',
		area: null,
		city: null,
		province: null,
		district: null,
		keywords: null,

		// district: '',
	})



	onLoad((options) => {
		getAreaCity();
		getList();
		getBanner();
		// #ifdef MP
		// 小程序识别二维码
		if (options.scene) {
			const sceneParams = decodeURIComponent(options.scene).split('=');
			options[sceneParams[0]] = sceneParams[1];
		}
		// #endif

		// 预览模板
		if (options.templateId) {
			sheep.$store('app').init(options.templateId);
		}

		// 解析分享信息
		if (options.spm) {
			$share.decryptSpm(options.spm);
		}

		// 进入指定页面(完整页面路径)
		if (options.page) {
			sheep.$router.go(decodeURIComponent(options.page));
		}
	});
	
	//轮播图跳转
	function swiperJump(item) {
		console.log('轮播图跳转事件：',item.url);
		const tabBarPages = [
		    '/pages/index/index',
		    '/pages/index/fabu',
		    '/pages/index/user'
		  ];
		console.log('tabbarsList：',tabBarPages);
		if(item.type == "in") {
			console.log('跳进内页');
			if(tabBarPages.includes(item.url)) {
				console.log('导航页');
				uni.switchTab({
					url: item.url,
				})
			}else {
				uni.navigateTo({
					url: item.url,
				})
			}
		}
	}

	// 下拉刷新
	onPullDownRefresh(() => {
		sheep.$store('app').init();
		setTimeout(function() {
			uni.stopPullDownRefresh();
		}, 800);
	});

	onPageScroll(() => {});
	
	const bannerList = ref([])
	async function getBanner() {
		const res = await sheep.$api.home.homeBanner({});
		console.log('banner',res);
		if(res.code == 1) {
			bannerList.value = res.data.list
			console.log('bannerList',bannerList.value);
		}
		
	}


	const toPage = (e) => {
		if (isLogin.value == true) {
			uni.navigateTo({
				url: e
			})
		} else {

			uni.showToast({
				title: '请先登录',
				icon: 'none',
				duration: 2000
			})

			setTimeout(function() {
				uni.switchTab({
					url: '/pages/index/user'
				})
			}, 2000);

		}
	}

	const rentList = ref([])
	//招租列表
	async function getList() {
		const res = await sheep.$api.rent.rentlist({
			keywords: listQuery.value.keywords,
			page: listQuery.value.page,
			limit: 25,
			cate_ids: listQuery.value.cate_ids,
			order: 'normal',
			status: 1,
			recommend: 1,
			province: listQuery.value.province,
			city: listQuery.value.city,

		});
		console.log('getList', res);

		if (res.data && res.data.list) {
			rentList.value = res.data.list;

			// Safely process each item
			rentList.value = rentList.value.map(item => {
				// Create a new object with all properties from the original item
				const newItem = {
					...item
				};

				// Only process release_time_text if it exists
				if (newItem.release_time_text) {
					newItem.release_time_text = newItem.release_time_text.substring(0, 10);
				}

				return newItem;
			});
		} else {
			// Handle case where data is not in expected format
			rentList.value = [];
		}

		// rentList.value = res.data.list;
		// for(let i=0;i<rentList.value.length;i++){
		// 	rentList.value[i].release_time_text = rentList.value[i].release_time_text.substring(0, 10)
		// }
		console.log('getList', rentList.value);
	}
	
	// 隐藏原生tabBar
	uni.hideTabBar({
		fail: () => {},
	});
</script>

<style lang="scss" scoped>
	.search-result {
		padding-top: 10px;
		padding-bottom: 20px;
		text-align: center;
	}

	.search-result-text {
		text-align: center;
		font-size: 14px;
		color: #666;
	}

	.example-body {
		/* #ifndef APP-NVUE */
		display: block;
		/* #endif */
		padding: 0px;
	}

	.uni-mt-10 {
		margin-top: 10px;
	}

	.title {
		color: #3d3d3d;
		font-size: 34rpx;
		font-weight: 800;
		line-height: 34rpx;
	}

	.intro {
		font-size: 24rpx;
		font-weight: 400;
		line-height: 24rpx;
	}

	.title2 {
		color: #3d3d3d;
		font-size: 30rpx;
		font-weight: 800;
		line-height: 42rpx;
	}

	.title3 {
		font-size: 20rpx;
		font-weight: 400;
		color: #999999;
		line-height: 22rpx;
	}

	.container {
		min-height: 90vh;
		width: 100%;
		background-color: #f7f7f7;
		// display: grid;
		// background-color: linear-gradient(to bottom, #fcc74e 0%, #f7f7f7 50%);
		// background-size: 100% 100%;

		.top {
			height: 500rpx;
			background-image: url('https://jiangxiaoxian.0rui.cn/topBack.png');
			background-size: 100%;
			padding: 30rpx 30rpx 30rpx 30rpx;
			display: grid;

			.locTop {
				width: 100%;
				height: 70rpx;
				display: flex;
				justify-content: space-between;
				// align-items: center;

				.locTop-left {
					width: 330rpx;
					display: flex;
					align-items: center;
					justify-content: flex-start;

				}

				.locTop-right {
					width: 360rpx;
					height: 70rpx;

				}
			}

			.navTop {
				margin-top: 30rpx;
				display: flex;
				justify-content: space-between;
				// align-items: center;

				.navTop-left {
					width: 270rpx;
					height: 350rpx;
					background-image: url('https://jiangxiaoxian.0rui.cn/rent.png');
					background-size: 100%;
					border-radius: 18rpx;
					padding: 40rpx 30rpx 0rpx 30rpx;

				}

				.navTop-right {
					display: grid;
					justify-content: center;
					align-items: center;
					width: 330rpx;

					.navRight1 {
						width: 270rpx;
						height: 140rpx;
						background-image: url('https://jiangxiaoxian.0rui.cn/school.png');
						background-size: 100%;
						border-radius: 18rpx;
						padding: 40rpx 30rpx 0rpx 30rpx;
					}

					.navRight2 {
						margin-top: 30rpx;
						width: 270rpx;
						height: 140rpx;
						background-image: url('https://jiangxiaoxian.0rui.cn/shoppingMall.png');
						background-size: 100%;
						border-radius: 18rpx;
						padding: 40rpx 30rpx 0rpx 30rpx;
					}
				}
			}


		}

		.swiper-box {
			width: 690rpx;
			padding: 0 30rpx;
			// display: grid;
			// justify-content: center;
			height: 280rpx;
			position: relative;

			.swiper_s {
				width: 100%;
				height: 100%;
				// display: grid;
				// justify-content: center;

				.swiper-image {
					width: 100%;
					height: 100%;
					border-radius: 36rpx;
					object-fit: contain;
				}
			}

		}

		.recommend-box {
			margin-top: 30rpx;
			padding: 20rpx 30rpx;

			.recom-titleBox {
				display: flex;
				justify-content: space-between;
				align-items: center;
				width: 690rpx;

				.title-more {
					display: flex;
					justify-content: center;
					align-items: center;
				}
			}

			.rrecommList {
				margin-top: 30rpx;
				// padding: 30rpx;

				.reListItem {
					background-color: #ffffff;
					width: 640rpx;
					height: 190rpx;
					padding: 30rpx;
					display: flex;
					justify-content: flex-start;
					align-items: center;
					border-radius: 18rpx;

					.listItem-images {
						display: grid;
						justify-content: flex-start;

						.iamges-mainImg {
							width: 190rpx;
							height: 190rpx;
							position: relative;
							right: 0rpx;
							bottom: 18rpx;
							z-index: 1;
							border-radius: 12rpx;
						}
					}

					.listItem-contents {
						margin-left: 20rpx;

						.score-box {
							display: flex;

							.cate {
								display: flex;
								justify-content: flex-start;
								align-items: center;

								.cate-e {

									.status-tag {
										padding: 5rpx 15rpx;
										border-radius: 5rpx;
										font-size: 22rpx;
										font-weight: 400;
										line-height: 22rpx;
										background-color: rgba(247, 97, 0, 0.2);

										// opacity: 0.2;
										color: #F76100;
										//flex-shrink: 0
									}
								}
							}



						}


						.cons-third {
							margin-top: 20rpx;
							width: 430rpx;
							display: flex;
							justify-content: space-between;
							align-items: center;
						}
					}
				}

			}

		}


	}
</style>