{"id": "shopro", "name": "shopro", "displayName": "Shopro商城", "version": "3.1.0", "description": "Shopro-B2C商城，一套代码，同时发行到iOS、Android、H5、微信小程序多个平台，请使用手机扫码快速体验强大功能", "scripts": {"prettier": "prettier --write  \"{pages,sheep}/**/*.{js,json,tsx,css,less,scss,vue,html,md}\""}, "repository": "https://github.com/sheepjs/shop.git", "keywords": ["商城", "B2C", "<PERSON><PERSON>", "商城模板"], "author": "", "license": "MIT", "homepage": "https://www.shopro.top", "dcloudext": {"category": ["前端页面模板", "uni-app前端项目模板"], "sale": {"regular": {"price": "0.00"}, "sourcecode": {"price": "0.00"}}, "contact": {"qq": ""}, "declaration": {"ads": "无", "data": "无", "permissions": "无"}, "npmurl": ""}, "uni_modules": {"dependencies": [], "encrypt": [], "platforms": {"cloud": {"tcb": "u", "aliyun": "u"}, "client": {"App": {"app-vue": "y", "app-nvue": "u"}, "H5-mobile": {"Safari": "y", "Android Browser": "y", "微信浏览器(Android)": "y", "QQ浏览器(Android)": "y"}, "H5-pc": {"Chrome": "y", "IE": "y", "Edge": "y", "Firefox": "y", "Safari": "y"}, "小程序": {"微信": "y", "阿里": "u", "百度": "u", "字节跳动": "u", "QQ": "u", "京东": "u"}, "快应用": {"华为": "u", "联盟": "u"}, "Vue": {"vue2": "u", "vue3": "y"}}}}, "dependencies": {"@hyoga/uni-socket.io": "^1.0.1", "dayjs": "^1.11.9", "lodash": "^4.17.21", "luch-request": "^3.1.0", "pinia": "2.1.4", "pinia-plugin-persist-uni": "1.2.0", "qs-canvas": "^1.0.11", "weixin-js-sdk": "^1.6.0"}, "devDependencies": {"prettier": "^3.0.0", "vconsole": "^3.15.1"}}