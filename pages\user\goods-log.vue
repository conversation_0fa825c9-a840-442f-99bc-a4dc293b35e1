<template>
	<s-layout title="我的足迹" :bgStyle="{ color: '#f2f2f2' }">
		<view class="cart-box ss-flex ss-flex-col ss-row-between">
			<scroll-view @scrolltolower="onScrolltolower" :refresher-enabled="true" :refresher-triggered="homrS"
				@refresherrefresh="onS" scroll-y="true" class="flex align-items"
				style="height: 87vh;box-sizing: border-box;">
				<view class="cart-content">
					<view class="goods-box ss-r-10 ss-m-b-14" v-for="(item, index) in feetHisList" :key="index">
						<view class="dataBox">{{item.date_text}}</view>
						<view class="rrecommList" v-for="(e,i) in item.log" :key="i" @click="toDetail(e)">
							<view class="reListItem">
								<!-- 左侧图片 -->
								<view class="listItem-images">
									<image v-if="e.information.recommend == 1"
										style="width: 77rpx;height: 36rpx;z-index: 100;position: relative;top: 34rpx;left: 0rpx;"
										src="https://jiangxiaoxian.0rui.cn/hot.png"></image>
									<image v-if="e.information.recommend != 1"
										style="width: 77rpx;height: 36rpx;z-index: 100;position: relative;top: 34rpx;left: 0rpx;"
										src=""></image>
									<image class="iamges-mainImg" :src="e.information.images[0]" mode="aspectFill"></image>
								</view>
								<!-- 右侧内容 -->
								<view class="listItem-contents">
									<view class="title2" style="margin-bottom: 20rpx;">{{e.information.platform_title}}
									</view>
									<scroll-view class="score-box">
										<view class="fs11 flex cate">
											<view class="cate-e" v-for="(w,q) in e.information.cate" :key="q">
												<view v-show="w" class="status-tag " style="margin-right: 20rpx;">{{w}}
												</view>
											</view>
										</view>
									</scroll-view>
									<view class="cons-third">
										<view style="display: flex;">
											<image style="width: 24rpx;height: 24rpx;"
												src="https://jiangxiaoxian.0rui.cn/locationList.png"></image>
											<view class="title3" style="margin-left: 6rpx;">
												{{e.information.address_city_text}}
											</view>
										</view>
										<view class="title3">{{e.information.release_time_text.split(' ')[0]}}</view>
										<view style="display: flex;">
											<image style="width: 26rpx;height: 20rpx;"
												src="https://jiangxiaoxian.0rui.cn/eye.png"></image>
											<view class="title3" style="margin-left: 6rpx;">{{e.information.views}}
											</view>
										</view>
									</view>
								</view>
							</view>
						</view>

					</view>
				</view>

				<view class="flex flex-column flex-start align-items" v-if="feetHisList.length == 0"
					style="margin-top: 300rpx;">
					<image src="https://jiangxiaoxian.0rui.cn/noneList.png" mode=""
						style="width: 180rpx;height: 180rpx;">
					</image>
					<view style="margin-top: 30rpx;font-size: 28rpx;color: #323232;">暂无数据信息</view>
				</view>
			</scroll-view>


		</view>
		<!-- <uni-load-more v-if="state.pagination.total > 0" :status="state.loadStatus" :content-text="{
        contentdown: '上拉加载更多',
      }" @tap="loadmore" />
		<s-empty v-if="state.pagination.total === 0" text="暂无浏览记录" icon="/static/collect-empty.png" /> -->
	</s-layout>
</template>

<script setup>
	import sheep from '@/sheep';
	import {
		ref,
		reactive
	} from 'vue';
	import {
		onLoad,
		onReachBottom
	} from '@dcloudio/uni-app';
	import _ from 'lodash';
	import dayjs from 'dayjs';

	const date = new Date();
	const year = date.getFullYear();
	const month = String(date.getMonth() + 1).padStart(2, '0'); // 补零
	const day = String(date.getDate()).padStart(2, '0'); // 补零

	const today = `${year}-${month}-${day}`;
	// const today = ref('')

	const feetHisList = ref([])
	const homrS = ref('')
	const feetCount = ref(0);
	const loadStatus = ref('');
	// 接口传参
	const listQuery = ref({
		page: 1,
		limit: 10,
		addrLatitude: null,
		addrLongitude: null,
		address: null,
		address1: null,
		categoryId: null,
		tags: '',
		cate_ids: '',
		area: null,
		city: null,
		province: null,
		district: null,
		keywords: null,

		// district: '',
	})

	onLoad(() => {
		getFeetList();
	});

	async function getFeetList() {
		const res = await sheep.$api.user.goodsLog.list({
			keywords: listQuery.value.keywords,
			page: listQuery.value.page,
			limit: listQuery.value.limit,
		});
		console.log('足迹返值', res);
		if (res.code == 1) {
			if (listQuery.value.page == 1) {
				feetHisList.value = res.data.list;
			} else {
				if (feetHisList.value.length > 0 && res.data.list.length > 0) {
					const lastItemOfPrevPage = feetHisList.value[feetHisList.value.length - 1];
					const firstItemOfNewPage = res.data.list[0];

					// 判断日期是否相同
					if (isSameDate(lastItemOfPrevPage.date, firstItemOfNewPage.date)) {
						// 合并相同日期的数据
						lastItemOfPrevPage.log = [...lastItemOfPrevPage.log, ...firstItemOfNewPage.log];
						// 添加剩余的数据
						feetHisList.value = [...feetHisList.value, ...res.data.list.slice(1)];
					} else {
						// 日期不同，直接拼接
						feetHisList.value = [...feetHisList.value, ...res.data.list];
					}
				} else {
					feetHisList.value = [...feetHisList.value, ...res.data.list];
				}
			}
			// feetHisList.value = [...feetHisList.value, ...res.data.list]
			feetCount.value = res.data.count
			console.log('足迹列表', feetHisList.value);
		}

	}

	// 辅助函数：比较两个日期是否相同
	function isSameDate(date1, date2) {
		return date1 === date2;
	}

	//加载更多
	function onScrolltolower() {
		if (feetHisList.value.length < feetCount.value) {
			listQuery.value.page += 1;
			getFeetList();
		}
	}
	//下拉刷新
	function onS() {
		homrS.value = true
		listQuery.value.keywords = ''
		uni.showLoading({
			title: '加载中...'
		});
		resetLists();
		setTimeout(() => {
			homrS.value = false;
			uni.hideLoading();
			uni.stopPullDownRefresh();
		}, 2000)
	}

	// 重置列表
	function resetLists() {
		listQuery.value.page = 1;
		feetHisList.value = [];
		loadStatus.value = "loading";
		getFeetList();
	}

	function toDetail(e) {
		console.log('跳转详情', e);
		uni.navigateTo({
			url: '/pages/canteenRent/rentDetail?id=' + e.information.id
		})
	}

	const sys_navBar = sheep.$platform.navbar;
	onReachBottom(() => {});
</script>

<style lang="scss" scoped>
	.title {
		color: #3d3d3d;
		font-size: 34rpx;
		font-weight: 800;
		line-height: 34rpx;
	}

	.intro {
		font-size: 24rpx;
		font-weight: 400;
		line-height: 24rpx;
	}

	.title2 {
		color: #3d3d3d;
		font-size: 30rpx;
		font-weight: 800;
		line-height: 42rpx;
	}

	.title3 {
		font-size: 20rpx;
		font-weight: 400;
		color: #999999;
		line-height: 22rpx;
	}

	.cart-box {
		.cart-header {
			height: 70rpx;
			background-color: #f6f6f6;
			width: 100%;
			position: fixed;
			left: 0;
			top: v-bind('sys_navBar') rpx;
			z-index: 1000;
			box-sizing: border-box;
		}

		.cart-footer {
			height: 100rpx;
			background-color: #fff;

			.pay-btn {
				height: 80rpx;
				line-height: 80rpx;
				border-radius: 40rpx;
				padding: 0 40rpx;
				min-width: 200rpx;
			}
		}

		.cart-content {
			width: 100%;
			padding: 0 30rpx;
			box-sizing: border-box;
			margin-top: 30rpx;

			.goods-box {
				// background-color: #fff;

				.dataBox {
					margin: 30rpx 0;
				}

				.rrecommList {
					margin-top: 30rpx;
					// padding: 30rpx;

					.reListItem {
						background-color: #ffffff;
						width: 640rpx;
						height: 190rpx;
						padding: 30rpx;
						display: flex;
						justify-content: flex-start;
						align-items: center;
						border-radius: 18rpx;

						.listItem-images {
							display: grid;
							justify-content: flex-start;

							.iamges-mainImg {
								width: 190rpx;
								height: 190rpx;
								position: relative;
								right: 0rpx;
								bottom: 18rpx;
								z-index: 1;
								border-radius: 12rpx;
							}
						}

						.listItem-contents {
							margin-left: 20rpx;

							.score-box {
								display: flex;

								.cate {
									display: flex;
									justify-content: flex-start;
									align-items: center;

									.cate-e {

										.status-tag {
											padding: 5rpx 15rpx;
											border-radius: 5rpx;
											font-size: 22rpx;
											font-weight: 400;
											line-height: 22rpx;
											background-color: rgba(247, 97, 0, 0.2);

											// opacity: 0.2;
											color: #F76100;
											//flex-shrink: 0
										}
									}
								}



							}


							.cons-third {
								margin-top: 20rpx;
								width: 430rpx;
								display: flex;
								justify-content: space-between;
								align-items: center;
							}
						}
					}

				}


				&:last-child {
					margin-bottom: 40rpx;
				}
			}
		}
	}

	.title-card {
		padding: 36rpx 0 46rpx 20rpx;

		.img-box {
			width: 164rpx;
			height: 164rpx;

			.order-img {
				width: 164rpx;
				height: 164rpx;
			}
		}

		.check-box {
			height: 100%;
		}

		.title-text {
			font-size: 28rpx;
			font-weight: 500;
			color: #333333;
		}

		.params-box {
			.params-title {
				height: 38rpx;
				background: #f4f4f4;
				border-radius: 2rpx;
				font-size: 24rpx;
				font-weight: 400;
				color: #666666;
			}
		}

		.price-text {
			color: $red;
			font-family: OPPOSANS;
		}
	}
</style>