<template>
	<s-layout title="我的发布">
		<view class="container">
			<!-- tabs页签 -->
			<view class="tabs-box">
				<su-tabs :list="tabMaps" @change="onChange" :scrollable="false" :current="currentTab"></su-tabs>
			</view>

			<!-- 推荐列表 -->
			<scroll-view @scrolltolower="onScrolltolower" :refresher-enabled="true" :refresher-triggered="homrS"
				@refresherrefresh="onS" scroll-y="true" class="flex align-items"
				style="height: 85vh;box-sizing: border-box;">
				<view class="recommend-box">
					<view class="rrecommList" v-for="(item,index) in rentList" :key="index" @click="toDetail(item)">
						<!-- 通过的 -->
						<view class="reListItem" v-if="item.status == 1">
							<view class="reItemCon">
								<!-- 左侧图片 -->
								<view class="listItem-images">
									<image class="iamges-mainImg" :src="item.images[0]" mode="aspectFill"></image>
								</view>
								<!-- 右侧内容 -->
								<view class="listItem-contents">
									<view class="title2" style="margin-bottom: 20rpx;">
										{{item.title}}
									</view>
									<scroll-view class="score-box">
										<view class="fs11 flex cate">
											<view class="cate-e" v-for="(e,i) in item.cate" :key="i">
												<view v-show="e" class="status-tag " style="margin-right: 20rpx;">{{e}}
												</view>
											</view>
										</view>
									</scroll-view>
									<view class="cons-third">
										<view style="display: flex;">
											<image style="width: 24rpx;height: 24rpx;"
												src="https://jiangxiaoxian.0rui.cn/locationList.png"></image>
											<view class="title3" style="margin-left: 10rpx;">{{item.address_city_text}}
											</view>
										</view>
										<view class="title3">{{item.release_time_text}}</view>
										<view style="display: flex;">
											<image style="width: 26rpx;height: 20rpx;"
												src="https://jiangxiaoxian.0rui.cn/eye.png"></image>
											<view class="title3" style="margin-left: 10rpx;">{{item.views}}</view>
										</view>
									</view>
								</view>
							</view>
							<view
								style="background-color: #f7f7f7;height: 1rpx;width: 630rpx;border-radius: 12rpx;margin: 30rpx 0;">
							</view>
							<view class="reItemBtn">
								<view class="editItemBtn" @click.stop="rentEdit(item)">修改信息</view>
								<view class="cancelItemBtn" @click.stop="rentCancel(item)">取消发布</view>
							</view>
						</view>

						<!-- 待审核的的 -->
						<view class="pandListItem" v-if="item.status == 0">
							<view class="pandItemCon">
								<!-- 左侧图片 -->
								<view class="listItem-images">
									<image v-if="item.status == 0"
										style="width: 151rpx;height: 210rpx;z-index: 100;position: relative;top: 78rpx;right: 15rpx;"
										src="https://jiangxiaoxian.0rui.cn/releasePanding.png"></image>
									<image class="iamges-mainImg" :src="item.images[0]" mode="aspectFill"></image>
								</view>
								<!-- 右侧内容 -->
								<view class="listItem-contents">
									<view class="title2" style="margin-bottom: 20rpx;">
										{{item.title}}
									</view>
									<scroll-view class="score-box">
										<view class="fs11 flex cate">
											<view class="cate-e" v-for="(e,i) in item.cate" :key="i">
												<view v-show="e" class="status-tag " style="margin-right: 20rpx;">{{e}}
												</view>
											</view>
										</view>
									</scroll-view>
									<view class="cons-third">
										<view style="display: flex;">
											<image style="width: 24rpx;height: 24rpx;"
												src="https://jiangxiaoxian.0rui.cn/locationList.png"></image>
											<view class="title3" style="margin-left: 10rpx;">{{item.address_city_text}}
											</view>
										</view>
										<view class="title3">{{item.release_time_text}}</view>
										<view style="display: flex;">
											<image style="width: 26rpx;height: 20rpx;"
												src="https://jiangxiaoxian.0rui.cn/eye.png"></image>
											<view class="title3" style="margin-left: 10rpx;">{{item.views}}</view>
										</view>
									</view>
								</view>


							</view>
							<view
								style="background-color: #f7f7f7;height: 1rpx;width: 630rpx;border-radius: 12rpx;margin: 30rpx 0;">
							</view>
							<view class="reItemBtn">
								<view class="editItemBtn" @click.stop="rentEdit(item)">修改信息</view>
								<view class="cancelItemBtn" @click.stop="rentCancel(item)">取消发布</view>
							</view>
						</view>

						<!-- 未通过的 -->
						<view class="unListItem" v-if="item.status == 2">
							<view class="unItemCon">
								<!-- 左侧图片 -->
								<view class="listItem-images">
									<image v-if="item.status == 2"
										style="width: 151rpx;height: 210rpx;z-index: 100;position: relative;top: 78rpx;right: 15rpx;"
										src="https://jiangxiaoxian.0rui.cn/releaseFail.png"></image>
									<image class="iamges-mainImg" :src="item.images[0]" mode="aspectFill"></image>
								</view>
								<!-- 右侧内容 -->
								<view class="listItem-contents">
									<view class="title2" style="margin-bottom: 20rpx;">
										{{item.title}}
									</view>
									<scroll-view class="score-box">
										<view class="fs11 flex cate">
											<view class="cate-e" v-for="(e,i) in item.cate" :key="i">
												<view v-show="e" class="status-tag " style="margin-right: 20rpx;">{{e}}
												</view>
											</view>
										</view>
									</scroll-view>
									<view class="cons-third">
										<view style="display: flex;">
											<image style="width: 24rpx;height: 24rpx;"
												src="https://jiangxiaoxian.0rui.cn/locationList.png"></image>
											<view class="title3" style="margin-left: 10rpx;">{{item.address_city_text}}
											</view>
										</view>
										<view class="title3">{{item.release_time_text}}</view>
										<view style="display: flex;">
											<image style="width: 26rpx;height: 20rpx;"
												src="https://jiangxiaoxian.0rui.cn/eye.png"></image>
											<view class="title3" style="margin-left: 10rpx;">{{item.views}}</view>
										</view>
									</view>
								</view>

							</view>
							<view class="failReasonBox">
								<view class="titltFail">驳回原因：{{item.reason}}</view>
							</view>
							<view class="reItemBtn">
								<view class="editItemBtn" @click.stop="rentEdit(item)">修改信息</view>
								<view class="cancelItemBtn" @click.stop="rentCancel(item)">取消发布</view>
							</view>

						</view>

					</view>

				</view>
			</scroll-view>

		</view>
	</s-layout>
</template>

<script setup>
	import sheep from '@/sheep';
	import {
		onLoad,
		onShow,
		onReachBottom
	} from '@dcloudio/uni-app';
	import {
		ref,
		reactive
	} from 'vue';
	import _ from 'lodash';

	const listQuery = ref({
		page: 1,
		limit: 10,
		list_rows: 10,
		status: '',
	})
	const tabMaps = [{
			name: '全部',
			value: '3',
		},
		{
			name: '待审核',
			value: '0',
		},
		{
			name: '未通过',
			value: '2',
		},
		{
			name: '已发布',
			value: '1',
		},
	];
	const currentTab = ref(0)
	const rentList = ref([])
	const rentCount = ref(0)
	//切换tabs
	function onChange(e) {
		console.log('onChange', e);
		currentTab.value = e.index
		console.log('切换tabs', currentTab.value);
		if (currentTab.value == 0) {
			listQuery.value.status = ''
		} else {
			listQuery.value.status = tabMaps[currentTab.value].value
		}
		rentList.value = []
		getList();
	}

	//招租列表
	async function getList() {
		const res = await sheep.$api.rent.rentlist({
			page: listQuery.value.page,
			limit: listQuery.value.limit,
			order: 'normal',
			status: listQuery.value.status,
			my: 1,
		});
		console.log('getList', res);

		if (res.data && res.data.list) {
			// rentList.value = res.data.list;
			rentList.value = [...rentList.value, ...res.data.list];
			rentCount.value = res.data.count;
			rentList.value = rentList.value.map(item => {
				const newItem = {
					...item
				};
				if (newItem.release_time_text) {
					newItem.release_time_text = newItem.release_time_text.substring(0, 10);
				}

				return newItem;
			});
		} else {
			rentList.value = [];
		}
		console.log('getList', rentList.value, rentCount.value);
	}
	//加载更多
	function onScrolltolower() {
		if (rentList.value.length < rentCount.value) {
			listQuery.value.page += 1;
			getList();
		}
	}
	//下拉刷新
	function onS() {
		homrS.value = true
		listQuery.value.keywords = ''
		uni.showLoading({
			title: '加载中...'
		});
		resetLists();

		setTimeout(() => {
			homrS.value = false;
			uni.hideLoading();
			uni.stopPullDownRefresh();
		}, 2000)
	}
	// 重置列表
	function resetLists() {
		listQuery.value.page = 1;
		rentList.value = [];
		getList();
	}
	//详情
	function toDetail(e) {
		console.log('跳转详情', e);
		uni.navigateTo({
			url: '/pages/canteenRent/rentDetail?id=' + e.id
		})
	}
	//修改
	function rentEdit(item) {
		console.log('修改招租',item);
		uni.navigateTo({
			url: `/pages/canteenRent/rentEdit?id=${item.id}`
		})
	}
	//取消
	function rentCancel(item) {
		console.log('取消招租',item.id);
		const ids = item.id
		console.log('取消招租',ids);
		sheep.$api.rent.delRent({ids:item.id}).then((res) => {
			if(res.code === 1) {
				console.log('delete:',res);
				uni.showToast({
					title: '取消成功',
					icon: 'none'
				})
				rentList.value = []
				getList()
			}else {
				uni.showToast({
					title: res.msg,
					icon: 'error'
				})
			}
		})
	}
	
	getList()
</script>

<style lang="scss" scoped>
	.title {
		color: #3d3d3d;
		font-size: 34rpx;
		font-weight: 800;
		line-height: 34rpx;
	}

	.intro {
		font-size: 24rpx;
		font-weight: 400;
		line-height: 24rpx;
	}

	.title2 {
		color: #3d3d3d;
		font-size: 30rpx;
		font-weight: 800;
		line-height: 42rpx;
	}

	.title3 {
		font-size: 20rpx;
		font-weight: 400;
		color: #999999;
		line-height: 22rpx;
	}

	.titltFail {
		font-size: 28rpx;
		font-weight: 400;
		line-height: 28rpx;
		color: #3d3d3d;
		display: flex;
		justify-content: flex-start;

	}

	.container {

		.recommend-box {
			padding: 0rpx 30rpx;

			.rrecommList {
				margin-top: 30rpx;

				.reListItem {
					background-color: #ffffff;
					width: 640rpx;
					height: 320rpx;
					padding: 30rpx;
					// display: block;
					// justify-content: ;
					// align-items: center;
					border-radius: 18rpx;
					margin-bottom: 30rpx;

					.reItemCon {
						width: 100%;
						display: flex;
						justify-content: flex-start;
						align-items: center;

						.listItem-images {
							display: grid;
							justify-content: flex-start;

							.iamges-mainImg {
								width: 190rpx;
								height: 190rpx;
								position: relative;
								right: 0rpx;
								bottom: 0rpx;
								z-index: 1;

							}
						}

						.listItem-contents {
							margin-left: 20rpx;

							.score-box {
								display: flex;

								.cate {
									display: flex;
									justify-content: flex-start;
									align-items: center;

									.cate-e {

										.status-tag {
											padding: 5rpx 15rpx;
											border-radius: 5rpx;
											font-size: 22rpx;
											font-weight: 400;
											line-height: 22rpx;
											background-color: rgba(247, 97, 0, 0.2);

											// opacity: 0.2;
											color: #F76100;
											//flex-shrink: 0
										}
									}
								}



							}


							.cons-third {
								width: 428rpx;
								margin-top: 26rpx;
								display: flex;
								justify-content: space-between;
								align-items: center;
							}
						}

					}





				}

				.unListItem {
					background-color: #ffffff;
					width: 640rpx;
					height: 440rpx;
					padding: 30rpx;
					border-radius: 18rpx;
					margin-bottom: 30rpx;

					.unItemCon {
						width: 100%;
						height: 190rpx;
						display: flex;
						justify-content: flex-start;
						align-items: center;

						.listItem-images {
							display: grid;
							justify-content: flex-start;

							.iamges-mainImg {
								width: 190rpx;
								height: 190rpx;
								position: relative;
								right: 0rpx;
								bottom: 108rpx;
								z-index: 1;

							}
						}

						.listItem-contents {
							margin-left: 20rpx;

							.score-box {
								display: flex;

								.cate {
									display: flex;
									justify-content: flex-start;
									align-items: center;

									.cate-e {

										.status-tag {
											padding: 5rpx 15rpx;
											border-radius: 5rpx;
											font-size: 22rpx;
											font-weight: 400;
											line-height: 22rpx;
											background-color: rgba(247, 97, 0, 0.2);

											// opacity: 0.2;
											color: #F76100;
											//flex-shrink: 0
										}
									}
								}



							}


							.cons-third {
								width: 428rpx;
								margin-top: 26rpx;
								display: flex;
								justify-content: space-between;
								align-items: center;
							}
						}

					}

					.failReasonBox {
						height: 69rpx;
						width: 570rpx;
						margin: 30rpx 0;
						padding: 30rpx;
						background-color: #f7f7f7;
						border-radius: 12rpx;
						overflow-wrap: break-word;
						word-break: break-word;
						white-space: normal;

					}

				}

				.pandListItem {
					background-color: #ffffff;
					width: 640rpx;
					height: 320rpx;
					padding: 30rpx;
					border-radius: 18rpx;
					margin-bottom: 30rpx;

					.pandItemCon {
						width: 100%;
						height: 190rpx;
						display: flex;
						justify-content: flex-start;
						align-items: center;

						.listItem-images {
							display: grid;
							justify-content: flex-start;

							.iamges-mainImg {
								width: 190rpx;
								height: 190rpx;
								position: relative;
								right: 0rpx;
								bottom: 108rpx;
								z-index: 1;

							}
						}

						.listItem-contents {
							margin-left: 20rpx;

							.score-box {
								display: flex;

								.cate {
									display: flex;
									justify-content: flex-start;
									align-items: center;

									.cate-e {

										.status-tag {
											padding: 5rpx 15rpx;
											border-radius: 5rpx;
											font-size: 22rpx;
											font-weight: 400;
											line-height: 22rpx;
											background-color: rgba(247, 97, 0, 0.2);

											// opacity: 0.2;
											color: #F76100;
											//flex-shrink: 0
										}
									}
								}



							}


							.cons-third {
								width: 428rpx;
								margin-top: 26rpx;
								display: flex;
								justify-content: space-between;
								align-items: center;
							}
						}

					}

				}

			}

		}

	}

	.reItemBtn {
		width: 100%;
		display: flex;
		justify-content: flex-end;
		align-items: center;

		.editItemBtn {
			width: 189rpx;
			height: 70rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			font-size: 32rpx;
			border-radius: 128rpx;
			border: 1rpx solid #333333;
			font-weight: 400;
			color: #333333;
			line-height: 20rpx;
		}

		.cancelItemBtn {
			width: 189rpx;
			height: 70rpx;
			background: linear-gradient(to right, #FFBD25, #FCCA58);
			margin-left: 20rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			font-size: 32rpx;
			border-radius: 128rpx;
			font-weight: 400;
			color: #333333;
			line-height: 20rpx;
		}

	}
</style>