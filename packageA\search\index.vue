<template>
	<s-layout title="搜索结果" :bgStyle="{ color: '#ffffff' }">
		<!-- <view>搜索页面</view> -->
		<view class="container">
			<view class="top">
				<!-- 顶部-地址&搜索 -->
				<view class="locTop">
					<view class="locTop-right">
						<uni-section style="border-radius: 192rpx;padding: 0;" type="line">
							<uni-search-bar v-model="listQuery.keywords" @confirm="search"
								style="border-radius: 192rpx;" radius="23" placeholder="搜索您需要的信息" bgColor="#EEEEEE"
								clearButton="none" cancelButton="none" />
						</uni-section>
						<!-- @confirm="search" -->
					</view>
				</view>
			</view>

			<!-- tabs页签 -->
			<view class="tabs-box">
				<su-tabs :list="tabMaps" @change="onChange" :scrollable="false" :current="currentTab"></su-tabs>
			</view>

			<scroll-view @scrolltolower="onScrolltolower" :refresher-enabled="true" :refresher-triggered="homrS"
				@refresherrefresh="onS" scroll-y="true" class="flex align-items"
				style="height: 1250rpx;box-sizing: border-box;">

				<!-- 食堂招租 -->
				<view class="recommend-box" v-if="currentTab == 0">
					<view class="rrecommList" v-for="(item,index) in collectRentList" :key="index">
						<view class="rrecommList">
							<view class="reListItem" v-for="(item,index) in rentList" :key="index"
								@click="toDetail(item)">
								<!-- 左侧图片 -->
								<view class="listItem-images">
									<image v-if="item.recommend == 1"
										style="width: 77rpx;height: 36rpx;z-index: 100;position: relative;top: 34rpx;left: 0rpx;"
										src="https://jiangxiaoxian.0rui.cn/hot.png"></image>
									<image class="iamges-mainImg" :src="item.images[0]" mode="aspectFill"></image>
								</view>
								<!-- 右侧内容 -->
								<view class="listItem-contents">
									<view class="title2" style="margin-bottom: 20rpx;">{{item.platform_title}}</view>
									<scroll-view class="score-box">
										<view class="fs11 flex cate">
											<view class="cate-e" v-for="(e,i) in item.cate" :key="i">
												<view v-show="e" class="status-tag " style="margin-right: 20rpx;">{{e}}
												</view>
											</view>

											<!-- <text v-show="item.cate[1]"
											class="status-tag" style="margin-right: 20rpx;">{{item.cate[1]}}</text>
										<text v-show="item.cate[2]"
											class="status-tag">{{item.cate[2]}}</text> -->
										</view>
									</scroll-view>
									<view class="cons-third">
										<view style="display: flex;">
											<image style="width: 24rpx;height: 24rpx;"
												src="https://jiangxiaoxian.0rui.cn/locationList.png"></image>
											<view class="title3" style="margin-left: 10rpx;">{{item.address_city_text}}
											</view>
										</view>
										<view class="title3">{{item.release_time_text}}</view>
										<view style="display: flex;">
											<image style="width: 26rpx;height: 20rpx;"
												src="https://jiangxiaoxian.0rui.cn/eye.png"></image>
											<view class="title3" style="margin-left: 10rpx;">{{item.views}}</view>
										</view>
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>


				<!-- 平台课程 -->
				<view class="exChangeBox" v-if="currentTab == 1">
					<view class="couponsList">
						<view class="list-item" v-for="(item,index) in classesList" :key="index"
							@click="toDetail(item)">
							<image style="width: 100%;height: 200rpx;border-radius: 30rpx 30rpx 0 0;" :src="item.image">
							</image>
							<image v-if="item.status == 1"
								style="width: 77rpx;height: 36rpx;z-index: 100;position: relative;bottom: 190rpx;left: 20rpx;"
								src="https://jiangxiaoxian.0rui.cn/schoolText.png"></image>
							<image v-if="item.status == 2"
								style="width: 77rpx;height: 36rpx;z-index: 100;position: relative;bottom: 190rpx;left: 20rpx;"
								src="https://jiangxiaoxian.0rui.cn/schoolVideo.png"></image>
							<view class="item-text">
								<view class="fs30 c3 classTitle" style="">{{item.title}}</view>
								<view class="cons-third">
									<view class="title3">{{item.release_time_text}}</view>
									<view style="display: flex;">
										<image style="width: 26rpx;height: 20rpx;"
											src="https://jiangxiaoxian.0rui.cn/eye.png">
										</image>
										<view class="title3" style="margin-left: 10rpx;">{{item.views}}</view>
									</view>
								</view>
							</view>

						</view>
					</view>


				</view>


				<!-- 原料商城 -->
				<view class="goodsBox" v-if="currentTab == 2">
					<view class="goodsList">
						<view class="list-item" v-for="(item,index) in mallList" :key="index" @click="sheep.$router.go('/pages/goods/index', { id: item.id })">
							<image style="width: 330rpx;height: 200rpx;border-radius: 18rpx 18rpx 0 0;"
								:src="item.image">
							</image>
							<view class="item-text">
								<view class="fs30 c3 goodsTitle" style="margin: 0 auto;width: 300rpx;">{{item.title}}</view>
								<view class="cons-third">
									<view class="title3">{{item.release_time_text}}</view>
									<view style="display: flex;">
										<image style="width: 26rpx;height: 20rpx;"
											src="https://jiangxiaoxian.0rui.cn/eye.png">
										</image>
										<view class="title3" style="margin-left: 10rpx;">{{item.views}}</view>
									</view>
								</view>
							</view>

						</view>
					</view>

				</view>

				<!-- 无数据展示 
				<view class="flex flex-column flex-start align-items" style="margin-top: 400rpx;"
					v-if="( currentTab == 0 && collectRentList.length == 0) || (currentTab == 1 && collectSchoolList.length == 0)">
					<image src="https://jiangxiaoxian.0rui.cn/noneList.png" mode=""
						style="width: 520rpx;height: 259rpx;">
					</image>
					<view style="margin-top: 30rpx;font-size: 28rpx;color: #323232;">暂无数据信息</view>
				</view>-->
			</scroll-view>



		</view>
	</s-layout>
</template>

<script setup>
	import sheep from '@/sheep';
	import {
		reactive,
		ref
	} from 'vue';
	import {
		onLoad,
		onReachBottom
	} from '@dcloudio/uni-app';
	import _ from 'lodash';

	// const
	const tabMaps = [{
			name: '食堂招租',
			value: '0',
		},
		{
			name: '平台学院',
			value: '1',
		},
		{
			name: '原料商城',
			value: '2',
		},
	];
	const currentTab = ref(0)
	const collectRentList = ref([])
	const rentCount = ref(0)
	const collectSchoolList = ref([])
	const schoolCount = ref(0)
	const mallList = ref([])
	const mallCount = ref(0)
	const homrS = ref(false)
	const loadStatus = ref('')
	const listQuery = ref({
		keywords: null,
		add_type: null,
		type: null,
		my: null,
		page: 1,
		list_rows: 10,
		rentPage: 1,
		schoolPage: 1,
		mallPage: 1,
		limit: 10,
		cate_ids: '',
		province: null,
		city: null,
		district: null,
		status: null,
		recommend: null,
		collect: null,
		order: null,
		nearby: null,
		latitude: null,
		longitude: '',
		area: null,
	})
	//切换tabs
	function onChange(e) {
		console.log('onChange', e);
		currentTab.value = e.index
		console.log('切换tabs', currentTab.value,listQuery.value.keywords);
		if(listQuery.value.keywords != null) {
			if (currentTab.value == 0) {
				collectRentList.value = [];
				getRentList();
			} else if (currentTab.value == 1) {
				collectSchoolList.value = [];
				getSchoolList();
			} else {
				getGoods();
			}
		}
	}

	function search(e) {
		console.log('搜索', e.value);
		if (currentTab.value == 0) {
			collectRentList.value = [];
			getRentList();
		} else if (currentTab.value == 1) {
			collectSchoolList.value = [];
			getSchoolList();
		} else {
			getGoods();
		}
	}

	//商品
	function getGoods() {
		console.log('商品', listQuery.value.keywords);
		// const listQuery =
		sheep.$api.goods.list({
			page: listQuery.value.mallPage,
			list_rows: listQuery.value.list_rows,
			keyword: listQuery.value.keywords,
		}).then((res) => {
			if (res.code == 1) {
				console.log('商品搜素列表:', res.data);
				mallList.value = res.data.data
				mallCount.value = res.data.total
			}
		})
	}

	//招租列表
	async function getRentList() {
		const res = await sheep.$api.rent.rentlist({
			keywords: listQuery.value.keywords,
			page: listQuery.value.rentPage,
			limit: listQuery.value.limit,
			order: 'normal',
			status: 1,
		});
		console.log('招租收藏列表', res);

		if (res.code == 1) {
			collectRentList.value = [...collectRentList.value, ...res.data.list];
			rentCount.value = res.data.count
		} else {
			// Handle case where data is not in expected format
			collectRentList.value = [];
			uni.showToast({
				title: res.msg,
				icon: 'none'
			})
		}
		console.log('getList', collectRentList.value);
	}
	//平台课程列表
	async function getSchoolList() {
		const res = await sheep.$api.school.schoolList({
			keywords: listQuery.value.keywords,
			page: listQuery.value.schoolPage,
			limit: listQuery.value.limit,
			order: 'normal',
		});
		console.log('平台课程收藏列表', res);

		if (res.code == 1) {
			collectSchoolList.value = [...collectSchoolList.value, ...res.data.list];
			schoolCount.value = res.data.count
		} else {
			// Handle case where data is not in expected format
			collectSchoolList.value = [];
			uni.showToast({
				title: res.msg,
				icon: 'none'
			})
		}
		console.log('平台课程收藏列表-222', collectSchoolList.value);
	}

	//加载更多
	function onScrolltolower() {
		if (currentTab.value == 0) {
			if (collectRentList.value.length < rentCount.value) {
				listQuery.value.rentPage += 1;
				getRentList();
			}
		} else if(currentTab.value == 1) {
			if (collectSchoolList.value.length < schoolCount.value) {
				listQuery.value.schoolPage += 1;
				getSchoolList();
			}
		}else {
			if (mallList.value.length < mallCount.value) {
				listQuery.value.mallPage += 1;
				getGoods();
			}
		}

	}
	//下拉刷新
	function onS() {
		homrS.value = true;
		listQuery.value.keywords = ''
		uni.showLoading({
			title: '加载中...'
		});
		resetLists();
		setTimeout(() => {
			homrS.value = false;
			uni.hideLoading();
			uni.stopPullDownRefresh();
		}, 2000)
	}
	// 重置列表
	function resetLists() {
		if (currentTab.value == 0) {
			listQuery.value.rentPage = 1;
			collectRentList.value = [];
			loadStatus.value = "loading";
			getRentList();
		} else if(currentTab.value == 1) {
			listQuery.value.schoolPage = 1;
			collectSchoolList.value = [];
			loadStatus.value = "loading";
			getSchoolList();
		}else {
			listQuery.value.mallPage = 1;
			mallList.value = [];
			loadStatus.value = "loading";
			getGoods();
		}
	}
</script>

<style lang="scss" scoped>
	.search-result {
		padding-top: 10px;
		padding-bottom: 20px;
		text-align: center;
	}
	
	.search-result-text {
		text-align: center;
		font-size: 14px;
		color: #666;
	}
	
	.example-body {
		/* #ifndef APP-NVUE */
		display: block;
		/* #endif */
		padding: 0px;
	}
	
	.uni-mt-10 {
		margin-top: 10px;
	}
	
	.title2 {
		color: #3d3d3d;
		font-size: 30rpx;
		font-weight: 800;
		line-height: 42rpx;
	}
	
	.title3 {
		font-size: 20rpx;
		font-weight: 400;
		color: #999999;
		line-height: 22rpx;
	}
	
	.container {

		.top {
			height: 70rpx;
			background-size: 100%;
			padding: 30rpx;
			display: grid;

			.locTop {
				width: 100%;
				height: 70rpx;
				display: flex;

				.locTop-right {
					width: 690rpx;
					height: 70rpx;

				}
			}


		}


		.recommend-box {
			padding: 0rpx 30rpx;

			.rrecommList {
				margin-top: 30rpx;

				.reListItem {
					background-color: #ffffff;
					width: 640rpx;
					// height: 190rpx auto;
					padding: 30rpx;
					display: flex;
					justify-content: flex-start;
					align-items: center;
					border-radius: 18rpx;

					.listItem-images {
						display: grid;
						justify-content: flex-start;

						.iamges-mainImg {
							width: 190rpx;
							height: 190rpx;
							position: relative;
							right: 0rpx;
							bottom: 18rpx;
							z-index: 1;

						}
					}

					.listItem-contents {
						width: 300rpx;
						height: auto;
						margin-left: 20rpx;

						.score-box {
							// width: 300rpx;
							display: flex;

							.cate {
								width: 300rpx;
								height: 190rpx auto;
								display: flex;
								justify-content: flex-start;
								align-items: center;
								white-space: nowrap;
								/* 禁止换行 */
								overflow: hidden;
								/* 超出部分隐藏 */

								.cate-e {

									.status-tag {
										padding: 5rpx 15rpx;
										border-radius: 5rpx;
										font-size: 22rpx;
										font-weight: 400;
										line-height: 22rpx;
										background-color: rgba(247, 97, 0, 0.2);
										width: 107rpx;
										height: 36rpx;
										display: flex;
										justify-content: center;
										align-items: center;
										color: #F76100;
									}
								}
							}

							.cate-e {
								width: 300rpx;
								height: 190rpx auto;
								display: flex;
								justify-content: flex-start;
								align-items: center;
								overflow-wrap: break-word;
								word-break: break-word;
								white-space: normal;

								.status-tag {
									// padding: 5rpx 15rpx;
									border-radius: 5rpx;
									font-size: 22rpx;
									font-weight: 400;
									line-height: 22rpx;
									background-color: rgba(247, 97, 0, 0.2);
									width: 107rpx;
									height: 36rpx;
									display: flex;
									justify-content: center;
									align-items: center;
									color: #F76100;
								}
							}

						}


						.cons-third {
							margin-top: 20rpx;
							display: flex;
							justify-content: flex-start;
							align-items: center;
						}
					}
				}

			}

		}


		.exChangeBox {
			width: 690rpx;
			margin: 0 auto;

			.couponsList {
				width: 100%;
				// padding: 0 30rpx;
				display: grid;
				grid-template-columns: repeat(2, 1fr);
				grid-gap: 20rpx;
				box-sizing: border-box;
				margin-top: 30rpx;


				.list-item {
					width: 330rpx;
					border-radius: 18rpx;
					box-shadow: 4rpx 4rpx 4rpx 4rpx rgba(153, 153, 153, 0.1);
					background-color: #ffffff;

					.item-text {
						padding: 0rpx 20rpx 0rpx 20rpx;
						height: 120rpx;
						position: relative;
						bottom: 30rpx;
						// text-align: center;

						.classTitle {
							height: 80rpx;
							width: 300rpx;
							display: -webkit-box;
							-webkit-line-clamp: 2;
							-webkit-box-orient: vertical;
							overflow: hidden;
							text-overflow: ellipsis;
						}

						.cons-third {
							margin-top: 20rpx;
							display: flex;
							justify-content: space-between;
							align-items: center;
						}

					}
				}

			}

		}

		.goodsBox {
			width: 690rpx;
			margin: 0 auto;

			.goodsList {
				width: 100%;
				// padding: 0 30rpx;
				display: grid;
				grid-template-columns: repeat(2, 1fr);
				grid-gap: 20rpx;
				box-sizing: border-box;
				margin-top: 30rpx;


				.list-item {
					width: 330rpx;
					border-radius: 18rpx;
					box-shadow: 4rpx 4rpx 4rpx 4rpx rgba(153, 153, 153, 0.1);
					background-color: #ffffff;

					.item-text {
						padding: 20rpx;
						min-height: 140rpx;
						position: relative;
						bottom: 0rpx;
						// text-align: center;

						.goodsTitle {
							min-height: 100rpx;
							width: 300rpx;
							display: -webkit-box;
							-webkit-line-clamp: 3;
							-webkit-box-orient: vertical;
							overflow: hidden;
							text-overflow: ellipsis;
						}

						.cons-third {
							margin-top: 20rpx;
							display: flex;
							justify-content: space-between;
							align-items: center;
						}




					}
				}

			}

		}

	}
</style>