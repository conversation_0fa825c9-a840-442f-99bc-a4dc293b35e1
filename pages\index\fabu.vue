<template>
	<!-- <view> 发布 </view> tabbar="/pages/index/fabu"-->
	<s-layout title="发布信息" :bgStyle="{ color: '#f7f7f7' }">
		<view class="container">
			<view class="backImg flex flex-column align-items">
				<view class="scroll-container box flex flex-column align-items">
					<view class="first flex flex-column align-items justify-start" style="padding: 30rpx;">
						<view class="row flex align-items" style="padding: 0 30rpx;">
							<view class="label flex align-items" style="width: auto;">
								标题
							</view>
							<view class="row-right" style="width: 80%;">
								<input type="text" placeholder="请输入标题信息(必填20字内)" class="input" v-model="form.title"
									style="width: 80%;text-align: right;" />
							</view>
						</view>
						<span class="line-row" style="margin: 30rpx 0;"></span>

						<view class="row flex align-items" style="justify-content: space-between;">
							<view class="label flex align-items" style="">
								地区
							</view>
							<view class="row-right" style="width: 500rpx;" @click="state.showRegion = true">
								<view v-if="form.area != '' " style="font-size: 28rpx;color: #3d3d3d">{{form.area}}
								</view>
								<view v-else style="font-size: 28rpx;color: #9C9C9C">请选择地区</view>
								<image style="width: 28rpx;height: 28rpx;margin-left: 10rpx;"
									src="https://jiangxiaoxian.0rui.cn/rightMore.png"></image>
							</view>
						</view>
						<span class="line-row" style="margin: 30rpx 0;"></span>

						<view class="row " style="">
							<view class="label flex align-items" style="width: auto;">
								详细地址
							</view>
							<view style="margin-top: 20rpx;height: 80rpx;">
								<textarea v-model="form.address_detail" style="height: 80rpx;width: 100%;"
									maxlength="400" placeholder-style="font-size:28rpx;color:#9c9c9c;line-height:36rpx"
									placeholder="请输入详细地址"></textarea>
							</view>

						</view>
						<span class="line-row" style="margin: 30rpx 0;"></span>

						<view class="row flex align-items" style="padding: 0 30rpx;">
							<view class="label flex align-items" style="width: auto;">
								联系人
							</view>
							<view class="row-right" style="width: 80%;">
								<input type="text" placeholder="请输入联系人" class="input" v-model="form.contacts"
									style="width: 80%;text-align: right;" />
							</view>
						</view>
						<span class="line-row" style="margin: 30rpx 0;"></span>

						<view class="row flex align-items" style="padding: 0 30rpx;">
							<view class="label flex align-items" style="width: auto;">
								联系电话
							</view>
							<view class="row-right" style="width: 80%;">
								<input type="number" placeholder="请输入联系电话" class="input" v-model="form.contact_number"
									style="width: 80%;text-align: right;" />
							</view>
						</view>

					</view>

					<!-- 详细信息 -->
					<view class="first flex flex-column align-items justify-start"
						style="margin-top: 20rpx;padding: 30rpx;">
						<view class="row align-items" style="">
							<view class="label flex align-items" style="width: auto;">
								详细信息
							</view>
							<view style="margin-top: 20rpx;height: 120rpx;margin-bottom: 30rpx;">
								<textarea v-model="form.content" style="height: 120rpx;width: 100%;" maxlength="400"
									placeholder-style="font-size:28rpx;color:#9c9c9c;line-height:36rpx"
									placeholder="请输入你的招标详细信息"></textarea>
							</view>

							<!-- 图片 -->
							<s-uploader v-model:url="state.formData.images" fileMediatype="image" limit="9" mode="grid"
								:imageStyles="{ width: '168rpx', height: '168rpx'}"></s-uploader>
							<view style="color: red;margin-top: 10rpx;font-size: 24rpx;">请上传尺寸大小为390*390的图片</view>
						</view>
						<span class="line-row" style="margin: 30rpx 0;"></span>

						<view class="row flex align-items" style="">
							<view class="label flex align-items ">
								项目类型
							</view>
							<picker :show="showType" @change="selectType" :value="typeShowList.indexOf(form.type)"
								:range="typeShowList">
								<view class="form_right"
									style="width: 500rpx;text-align: right;;font-size: 26rpx;font-weight: 400;color: #848484;">
									{{form.typeName||'请选择项目类型'}}
									<image style="width: 28rpx;height: 28rpx;margin-left: 10rpx;"
										src="https://jiangxiaoxian.0rui.cn/rightMore.png"></image>
								</view>
							</picker>
						</view>
						<span class="line-row" style="margin: 30rpx 0;"></span>

						<view class="row flex align-items" style="">
							<view class="label flex align-items" style="width: auto;">
								选择标签
							</view>
							<view class="row-right" style="width: 500rpx;" @click="openPopbq">
								<view style="font-size: 28rpx;color: #9C9C9C">选择</view>
								<!--v-if="form.tags == '' " <view v-else style="font-size: 28rpx;color: #3d3d3d;">河南</view> -->
								<image style="width: 28rpx;height: 28rpx;margin-left: 10rpx;"
									src="https://jiangxiaoxian.0rui.cn/rightMore.png"></image>
							</view>
						</view>
						<view class="checkedLabel" style="margin-top: 26rpx;" v-if="selectTagslist.length != 0">
							<view class="tagLab" v-for="(item, index) in selectTagslist" :key="index" style="">
								<view>
									{{ item.name }}
								</view>
								<view style="height: 30rpx;width: 1rpx;background-color: #f76100;margin: 0 20rpx;">
								</view>
								<image @click="removebq(index)" style="width: 20rpx;height: 20rpx;"
									src="https://jiangxiaoxian.0rui.cn/tagDelete.png" />
								<!-- <view style="display: flex;justify-content: center;align-items: center;
								width: 197rpx;height: 30rpx;
								padding: 10rpx 20rpx;background-color: rgba(247, 97, 0, 0.2);border-radius: 163rpx;">
									
								</view> -->

							</view>
						</view>
					</view>

				</view>

			</view>

			<view style="height: 200rpx;width: 100%;color: #f7f7f7;"></view>

			<view class="bottomBtn">
				<view style="margin:30rpx 0 0 0;display: flex;align-items: center;justify-content: center;">
					<view v-if="agreeAdd == false" style="width: 31rpx;height: 32rpx;
					border: 2rpx solid #999999;border-radius: 50%;" @click="show = true"></view>
					<image v-if="agreeAdd == true" style="width: 32rpx;height: 32rpx;"
						src="https://jiangxiaoxian.0rui.cn/fbxySelected.png"></image>
					<view style="font-size: 22rpx;line-height: 31rpx;color: #9C9C9C;display: flex;
					justify-content: center;align-items: center;margin-left: 20rpx;" @click="show = true">我已阅读并同意
						<view style="margin-left: 20rpx;color: #FCC74E;">《发布须知》</view>
					</view>
				</view>
				<view class="btns">
					<view class="submitPublish" @click="apply()">确认发布</view>
				</view>

			</view>

		</view>



		<!-- 须知 -->
		<su-popup @touchmove.native.stop.prevent :closeable="false" :show="show" :round="10" type="center"
			@close="close" @open="open" :safeAreaInsetBottom="false" :closeOnClickOverlay="false">
			<view style="width: 620rpx;height: 984rpx;padding: 30rpx;text-align: center;">
				<view style="font-size: 40rpx;font-weight: 800;height: 120rpx;margin: 0 auto;">《责任承诺确认书》</view>
				<scroll-view ref="scrollView" :scroll-top="scrollTop" :show-scrollbar='true'
					@scrolltolower="handleScroll" scroll-y="true" style="height: 900rpx;">
					<view class=" flex align-items flex-column">
						<rich-text style="text-align: justify;" :nodes="Negotiate"></rich-text>
					</view>
				</scroll-view>

			</view>
			<view
				style="width: 96%;display: flex;justify-content: space-between;align-items: center;margin-bottom: 30rpx;padding: 0 14rpx;">
				<view class="btn_4" @click="show = false">取消</view>
				<view class="btn_3" v-if="agreeShow == false">我同意</view>
				<view class="btn_2" v-if="agreeShow == true" @click="change()">我同意</view>
			</view>
		</su-popup>


		<!-- 身份证是否认证 -->
		<su-popup :show="cardShow == true" type="center" round="10" :isMaskClick="false">
			<view class="popupContacted">
				<view class="contactedBox">
					<image style="width: 198rpx;height: 122rpx;position: relative;left: 12rpx;"
						src="https://jiangxiaoxian.0rui.cn/couponSuccess.png"></image>
					<view
						style="font-size: 36rpx;line-height: 50rpx;font-weight: 900;color: #3d3d3d;text-align: center;">
						身份认证</view>
					<view
						style="font-size: 30rpx;line-height: 44rpx;font-weight: 400;color: #3d3d3d;text-align: center;">
						认证后即可发布信息</view>
				</view>
				<view class="contactBtn">
					<view class="tactBtnBox" style="color: #999999;" @click="cardShow = false">取消</view>
					<view class="tactBtnBox2" style="color: #323232;" @click="toAuthen()">去认证</view>
				</view>
			</view>
		</su-popup>


		<!-- 省市区弹窗 -->
		<su-regionCity-picker :show="state.showRegion" @cancel="state.showRegion = false" @confirm="onRegionConfirm">
		</su-regionCity-picker>


		<!-- 标签 -->
		<su-popup :show="showPopbq" mode="bottom" round="20"
			:customStyle="{ 'width': '750rpx', 'height': '1040rpx', 'zIndex': '999' }" :closeable="false"
			@close="closebq" :closeOnClickOverlay="false">
			<view class="popup_bq">
				<view class="flex align-items"
					style="width: 690rpx;height: 42rpx;padding: 30rpx;justify-content: space-between;">
					<view @click="closebq"
						style="z-index: 200;font-size: 28rpx;line-height: 39rpx;color: #9b9b9b;font-weight: 400;position: relative;left: 0;">
						取消</view>
					<view
						style="z-index: 200;font-size: 30rpx;line-height: 42rpx;color: #3d3d3d;font-weight: bold;position: relative;left: 0%;">
						选择标签（可选3个）</view>
					<view @click="checkTagTrue"
						style="z-index: 200;font-size: 28rpx;line-height: 39rpx;color: #333333;font-weight: 400;position: relative;right: 0%;">
						确定</view>
				</view>

				<view style="background-color: #d8d8d8;height: 2rpx;width: 100%;margin-bottom: 30rpx;"></view>

				<view class="flex flex-column w-100 bqlist">
					<scroll-view scroll-y="true" class="flex align-items allbqs">
						<view style="padding: 0 20rpx;margin: 0 auto;display: flex;flex-wrap: wrap;width: 100%;">
							<view style="box-sizing: border-box;display: grid; column-gap: 30rpx;"
								v-for="(item, index) in tagShowList" :key="index" @click="addbq(item, index)">
								<view class="selectedBox"
									:style="item.isSelect == true ? 'background-color: #fcc74e' : 'background-color: #f0f0f0'">
									{{ item.name }}
								</view>
							</view>
						</view>
					</scroll-view>
				</view>

			</view>
		</su-popup>

	</s-layout>

</template>

<script setup>
	import {
		onLoad,
		onShow,
		onReachBottom
	} from '@dcloudio/uni-app';
	import {
		ref,
		reactive,
		computed
	} from 'vue';
	import sheep from '@/sheep';
	import {
		consignee,
		mobile,
		address,
		region
	} from '@/sheep/validate/form';
	import {
		baseUrl
	} from '@/sheep/config';

	const isLogin = computed(() => sheep.$store('user').isLogin);
	const state = reactive({
		showRegion: false,
		model: {
			consignee: '',
			mobile: '',
			address: '',
			is_default: false,
			region: '',
		},
		rules: {
			consignee,
			mobile,
			address,
			region,
		},
		formData: {
			content: '',
			phone: '',
			images: [],
			type: '',
		},
		imageFiles: [],
	});
	const popupStyle = {
		width: '620rpx',
		padding: '50rpx 40rpx 42rpx 40rpx',
		height: '984rpx',
		margin: '0 auto', // 水平居中
		display: 'flex',
		justifyContent: 'start',
		alignItems: 'center',
		flexColumn: 'column'
	}
	const proCity = ref('')
	const form = ref({
		title: '',
		area: '',
		tags: '',
		contacts: '',
		contact_number: '',
		content: '',
		address_detail: '',
		type: '',
		typeName: '',
		images: '',
		province: '',
		city: '',
		cate_ids: '',
		cateName: '',
		imageList: [],
	})
	const onRegionConfirm = (e) => {
		console.log('onRegionConfirm', e);
		state.model = {
			...state.model,
			...e,
		};
		form.value.area = state.model.province_name + " " + state.model.city_name

		console.log('onRegionConfirm33', state.model, proCity.value);
		form.value.province = state.model.province_id
		form.value.city = state.model.city_id
		console.log('onRegionConfirm33', state.model, proCity.value);
		state.showRegion = false;
	};

	const show = ref(false) //发布须知显隐
	const agreeAdd = ref(false) //同意须知的勾选
	const agreeShow = ref(false) //须知的同意按钮
	const Negotiate = ref('') //富文本
	const cardShow = ref(false) //身份认证
	const status = ref(0)

	onShow(() => {
		getAuthentied();
		getFbKnow();
		getTagList();
		getTypeList();
	})
	//身份认证
	function getAuthentied() {
		sheep.$api.rent.isAuthentied().then((res) => {
			if (res.code == 1) {
				if (res.data.status == 1) {
					cardShow.value = false;
				} else {
					cardShow.value = true;
				}
			}
		})
	}
	//发布须知
	function getFbKnow() {
		sheep.$api.rent.fabuAgree().then((res) => {
			if (res.code == 1) {
				console.log('发布须知', res.data);
				Negotiate.value = res.data.notice_publication_agreement
				console.log('发布须知——Negotiate：', Negotiate.value);
			}
		})
	}

	function toAuthen() {
		if (isLogin.value == true) {
			cardShow.value = false
			uni.navigateTo({
				url: '/pages/user/authentication'
			})
		} else {

			uni.showToast({
				title: '请先登录',
				icon: 'none',
				duration: 2000
			})
			cardShow.value = false
			setTimeout(function() {
				uni.switchTab({
					url: '/pages/index/user'
				})
			}, 2000);

		}
	}

	async function getTagList() {
		const res = await sheep.$api.rent.tagsList({});
		if (res.code == 1) {
			tagList.value = res.data.list
			tagShowList.value = tagList.value.map(({
				name,
				id
			}) => ({
				name,
				id
			}));
			tagShowList.value.forEach(item => {
				item.isSelect = false;
			})
			console.log('tagList', tagList.value, tagShowList.value);
		}
	}

	const typeList = ref({})
	const typeShowList = ref([])
	async function getTypeList() {
		const res = await sheep.$api.rent.listType({});
		if (res.code == 1) {
			typeList.value = res.data
			// typeShowList.value = res.data

			typeShowList.value = Object.values(typeList.value)
			console.log('typeList', typeList.value, typeShowList.value);
		}
	}


	//项目类型
	const showType = ref(false)
	const typeIndex = ref(0); //已选择项目类型
	function closeType() {
		console.log('取消type', typeIndex.value);
		showType.value = false
	}

	function selectType(e) {
		console.log('选择项目类型', e);
		typeIndex.value = e.detail.value;
		console.log('选择项目类型--typeIndex', typeIndex.value);
		form.value.typeName = typeShowList.value[typeIndex.value]
		form.value.type = Object.keys(typeList.value).find(
			key => typeList.value[key] === form.value.typeName
		);
		console.log('type', form.value.type);
		showType.value = false;
	}


	//标签
	const showPopbq = ref(false)
	const tagList = ref([])
	const tagShowList = ref([])
	const selectTagslist = ref([]); //已选择标签
	const tagSelectedNum = ref(0)

	function openPopbq() {
		showPopbq.value = true;
	}

	function closebq() {
		showPopbq.value = false;
	}
	//选中标签
	function addbq(item, index) {
		if (tagShowList.value[index].isSelect == false) {
			if (tagSelectedNum.value < 3) {
				tagSelectedNum.value += 1;
				tagShowList.value[index].isSelect = true;
			} else {
				uni.showToast({
					title: '最多选择三个标签',
					icon: 'none'
				})
			}
		} else {
			if (tagSelectedNum.value > 0) {
				tagSelectedNum.value -= 1;
				tagShowList.value[index].isSelect = false;
			} else {
				uni.showToast({
					title: '至少选择一个',
					icon: 'none'
				})
			}
		}
		console.log('选择标签', tagSelectedNum.value);
	}

	function checkTagTrue() {
		console.log('确定选择的标签');
		selectTagslist.value = []
		for (let i = 0; i < tagShowList.value.length; i++) {
			if (tagShowList.value[i].isSelect == true) {
				selectTagslist.value.push(tagShowList.value[i])
			}
		}
		console.log('selectTagslist', selectTagslist.value);
		showPopbq.value = false
	}

	//删除标签（本地）
	function removebq(i) {
		console.log('删除标签1', i, selectTagslist.value[i].name);
		for (let w = 0; w < tagShowList.value.length; w++) {
			if (tagShowList.value[w].name == selectTagslist.value[i].name) {
				tagShowList.value[w].isSelect = false
			}
		}
		console.log('删除标签2', tagShowList.value);
		selectTagslist.value.splice(i, 1);
		tagSelectedNum.value -= 1;
		console.log('删除标签3', tagSelectedNum.value);
	}

	function change() {
		agreeAdd.value = true;
		show.value = false
	}
	//同意
	function handleScroll() {
		console.log(123)
		agreeShow.value = true
	}

	//详情
	function getDetail() {
		sheep.$api.rent.rentInfo(rentEditId.value).then((res) => {
			if (res.code == 1) {
				form.value = res.data,
					selectTagslist.value = tagList.value.reduce((acc, obj) => {
						if (form.value.cate_ids.includes(obj.id)) {
							acc.push(obj);
						}
						return acc;
					}, []);
				console.log('详情', selectTagslist.value);
			}
		})
	}

	async function apply() {
		if (agreeAdd.value == false) {
			uni.showToast({
				title: '请先同意发布须知',
				icon: 'none',
			})
			return
		}
		form.value.cate_ids = selectTagslist.value.map((item) => item.id).join(',')
		console.log('cate_ids', form.value.cate_ids, state.formData.images);

		form.value.images = state.formData.images.join(',');
		console.log('images', form.value.images);

		if (form.value.title == '') {
			uni.showToast({
				title: '请输入标题',
				icon: 'none',
			})
			return
		}
		if (form.value.province == '' && form.value.city == '') {
			uni.showToast({
				title: '请选择地区',
				icon: 'none',
			})
			return
		}
		if (form.value.address_detail == '') {
			uni.showToast({
				title: '请输入详细地址',
				icon: 'none',
			})
			return
		}
		if (form.value.contacts == '') {
			uni.showToast({
				title: '请输入联系人',
				icon: 'none',
			})
			return
		}
		if (form.value.contact_number == '') {
			uni.showToast({
				title: '请输入联系人电话',
				icon: 'none',
			})
			return
		}
		if (!/^1[3-9]\d{9}$/.test(form.value.contact_number)) {
			uni.showToast({
				title: '请输入正确的手机号',
				icon: 'none',
				duration: 2000
			})
			return;
		}
		if (form.value.content == '') {
			uni.showToast({
				title: '请输入详细信息',
				icon: 'none',
			})
			return
		}
		if (form.value.images == '') {
			uni.showToast({
				title: '请上传图片',
				icon: 'none',
			})
			return
		}
		if (form.value.type == '') {
			uni.showToast({
				title: '请选择项目类型',
				icon: 'none',
			})
			return
		}
		if (form.value.cate_ids == '') {
			uni.showToast({
				title: '请选择标签',
				icon: 'none',
			})
			return
		}
		console.log('apply——form:', form.value);

		const res = await sheep.$api.rent.addRent(form.value)
		if (res.code == 1) {
			console.log('发布成功');
			agreeAdd.value = false;
			selectTagslist.value = []
			form.value = {
				title: '',
				area: '',
				tags: '',
				contacts: '',
				contact_number: '',
				content: '',
				address_detail: '',
				type: '',
				images: '',
				province: '',
				city: '',
				cate_ids: '',
				cateName: '',
				imageList: [],
			}
			state.formData.images = []
			uni.switchTab({
				url: '/pages/index/user'
			})
		}

	}
</script>

<style lang="scss" scoped>
	.popupContacted {
		width: 660rpx;
		height: 477rpx;
		background-color: #fff;
		display: grid;
		border-radius: 18rpx;

		.contactedBox {
			width: 660rpx;
			height: 325rpx;
			padding: 30rpx 0;
			// margin-top: 40rpx;
			display: grid;
			justify-content: center;
			align-items: center;
		}

		.contactBtn {
			width: 660rpx;
			height: 92rpx;
			border-top: 1rpx solid #eeeeee;
			display: flex;
			border-radius: 0 0 18rpx 18rpx;
			// margin-top: 40rpx;

			.tactBtnBox {
				width: 330rpx;
				height: 92rpx;
				border-right: 1rpx solid #eeeeee;
				display: flex;
				align-items: center;
				justify-content: center;
			}

			.tactBtnBox2 {
				width: 330rpx;
				height: 92rpx;
				// border-right: 1rpx solid #eeeeee;
				display: flex;
				align-items: center;
				justify-content: center;
			}
		}

	}

	.checkedLabel {
		width: 690rpx;
		display: flex;
		justify-content: flex-start;
		flex-wrap: wrap;
		word-wrap: break-word;
		/* 允许长单词或URL换行 */
		overflow-wrap: break-word;
		align-items: center;

		.tagLab {
			width: 197rpx;
			height: 30rpx;
			margin-right: 20rpx;
			margin-bottom: 20rpx;
			// display: grid;
			// box-sizing: border-box;
			// column-gap: 20rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			text-align: center;
			padding: 10rpx 20rpx;
			background-color: rgba(247, 97, 0, 0.2);
			border-radius: 163rpx;
		}

	}


	.container {
		.backImg {
			height: 90vh;
			// background: linear-gradient(to bottom, #F1F2F8 0%, #F1F2F8 50%, #FFFFFF 100%);
			width: 100%;
			background-color: #f7f7f7;

			/* 添加新样式用于内部可滚动区域 */
			.scroll-container {
				margin: 0 auto;
				z-index: 1;
				/* 确保在.allbg之上 */
				overflow: visible;
				/* 允许内容溢出 */
				margin-top: 10rpx;
				// margin-bottom: 360rpx;

			}

			.box {
				width: 100%;

				.title {
					font-family: PingFang SC, PingFang SC;
					font-weight: 500;
					font-size: 32rpx;
					color: #C0C0C0;
					// margin: 42rpx 0 24rpx 0;

				}

				.reason {
					width: 100%;
					background: #FFFFFF;
					border-radius: 20rpx 20rpx 20rpx 20rpx;
					margin-top: 32rpx;
					font-family: PingFang SC, PingFang SC;
					font-weight: 500;
					font-size: 36rpx;
					color: #343434;
					line-height: 36rpx;
					padding: 30rpx;
					width: 630rpx;
					letter-spacing: 4.5rpx;
				}

				.first {
					width: 690rpx;
					// height: 266rpx;
					background: #FFFFFF;
					border-radius: 20rpx 20rpx 20rpx 20rpx;
					// margin-top: 32rpx;

					.row {
						width: 100%;
						margin-top: 7rpx;
						justify-content: space-between;

						.label {
							font-family: PingFang SC, PingFang SC;
							font-weight: bold;
							font-size: 30rpx;
							color: #343434;
							line-height: 32rpx;
							width: 160rpx;
						}




						.pic_view {
							width: 190rpx;
							height: 190rpx;
							background: #F7F7F7;
							border-radius: 10rpx;
							display: flex;
							justify-content: center;
							align-items: center;
							flex-flow: column;

							.texts {
								margin-top: 20rpx;
								font-size: 24rpx;
								color: #3D3D3D;
							}
						}

						.row-right {
							display: flex;
							justify-content: flex-end;
							align-items: center;
						}
					}

					.line-row {
						margin-top: 25rpx;
						width: 100%;
						height: 1rpx;
						background: #F0F0F0;
						border-radius: 0rpx 0rpx 0rpx 0rpx;
					}
				}

				::v-deep .plasty {
					text-align: right;
					z-index: 10000;
					color: #9c9c9c;
				}

				::v-deep .plasty_c {
					text-align: right;
					color: #FF4810;
				}

				::v-deep .bttop {
					font-size: 32rpx;
					color: #9C9C9C;
				}

				.second {
					width: 690rpx;
					// height: 340rpx;
					background: #FFFFFF;
					border-radius: 20rpx 20rpx 20rpx 20rpx;
					margin-top: 32rpx;
					margin-bottom: 32rpx;

					::v-deep .bttops {
						font-size: 28rpx;
						color: #9C9C9C;
						line-height: 36rpx;
					}
				}

				.third {
					width: 690rpx;

					.header {
						margin: 42rpx 0 24rpx 0;
						font-family: PingFang SC, PingFang SC;
						font-weight: 500;
						font-size: 28rpx;
						color: #343434;
						line-height: 32rpx;
					}
				}
			}


		}

	}






	.w-100 {
		width: 100%;
	}

	.flex {
		display: flex;
	}

	.justify-center {
		justify-content: center;
	}

	.justify-around {
		justify-content: space-around;
	}

	.space-between {
		justify-content: space-between;
	}

	.align-items {
		align-items: center;
	}

	.flex-column {
		flex-flow: column;
	}

	.justify-start {
		justify-content: start;
	}

	.mar-top-30 {
		margin-top: 30rpx;
	}


	.textarea_fb {
		::v-deep .u-textarea {
			height: 100%;
			padding: 0;
			border: none;
			font-size: 26rpx;
			color: #9C9C9C;
		}

		::v-deep .bttextarea {
			font-size: 24rpx;
			color: #9C9C9C;
		}
	}


	.shenfen ::v-deep .u-upload .u-upload__wrap__preview__image {
		width: 196rpx !important;
		height: 196rpx !important;
	}

	.bottomBtn {
		width: 750rpx;
		// height: 200rpx;
		background: #FFFFFF;
		border-radius: 0rpx 0rpx 0rpx 0rpx;
		position: fixed;
		bottom: 0;
		z-index: 10;

		.btns {
			width: 100%;
			display: flex;
			justify-content: center;
			align-items: center;
			margin-top: 30rpx;

			.submitPublish {
				margin-left: 20rpx;
				width: 100%;
				height: 98rpx;
				// border: 2rpx solid #323232;
				background-color: #fcc74e;
				color: #333333;
				font-family: YouSheBiaoTiHei;
				font-size: 36rpx;
				font-weight: 400;
				text-align: center;
				margin: 0 auto;
				display: grid;
				align-items: center;
			}

		}

	}

	.inputl {
		text-align: left;
		font-family: PingFang SC, PingFang SC;
		font-size: 30rpx;
		font-weight: 600;
		color: #343434;
		line-height: 32rpx;
		width: 500rpx;
	}

	.input {
		text-align: right;
		font-family: PingFang SC, PingFang SC;
		font-size: 28rpx;
		color: #343434;
		line-height: 32rpx;
		width: 500rpx;
	}

	.shenfen ::v-deep .u-transition.data-v-39e33bf2.vue-ref.u-fade-enter-to.u-fade-enter-active:not(:first-child) {
		margin-top: 20rpx;
	}

	.popup {
		// width: 690rpx;
		height: 950rpx;
		margin-top: 40rpx;


	}



	.popup-footer {
		display: flex;
		justify-content: center;
		align-items: center;
		// margin: 30rpx 0;
		height: 146rpx;

		.zhixiao {
			height: 80rpx;
			background: #E8E8E8;
			//border-radius: 401rpx 401rpx 401rpx 401rpx;
			font-family: YouSheBiaoTiHei;
			font-weight: 400;
			font-size: 36rpx;
			color: #9C9C9C;
			line-height: 32rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			position: absolute;
			width: 100%;
			bottom: 0;
			border-radius: 0rpx 0rpx 20rpx 20rpx;
		}

		.shows_zhidao {
			background-color: #323232;
			color: #BBFC5B;
			font-weight: 400;
			font-size: 36rpx;
		}
	}




	// 滚动条样式
	// ::v-deep ::-webkit-scrollbar {
	// 	/*滚动条整体样式*/
	// 	width: 4px !important;
	// 	height: 1px !important;
	// 	overflow: auto !important;
	// 	background: #ccc !important;
	// 	-webkit-appearance: auto !important;
	// 	display: block;
	// }

	// ::v-deep ::-webkit-scrollbar-thumb {
	// 	/*滚动条里面小方块*/
	// 	border-radius: 10px !important;
	// 	box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2) !important;
	// 	background: #7b7979 !important;
	// }

	// ::v-deep ::-webkit-scrollbar-track {
	// 	/*滚动条里面轨道*/
	// 	// box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2) !important;
	// 	// border-radius: 10px !important;
	// 	background: #FFFFFF !important;
	// }

	.textarea_mph {
		::v-deep .u-textarea {
			height: 100%;
			padding: 20rpx;
			border: none;
			font-size: 26rpx;
			color: #9C9C9C;
			background: #F7F7F7;
			border-radius: 18rpx;
		}

		::v-deep .bttextarea {
			font-size: 24rpx;
			color: #9C9C9C;
		}
	}

	.popup_type {
		display: grid;
		flex-direction: column;
		align-items: center;
		height: 550rpx;
		position: relative;

		.typelist {
			margin-top: 100rpx;
			z-index: 200;
			// margin-left: 80rpx;
			min-height: 500rpx;
			// padding: 30rpx;

			.selectedTypeBox {
				margin: 10rpx;
				border-radius: 163rpx;
				width: 140rpx auto;
				text-align: center;
				padding: 14rpx 22rpx;
			}

			.allTypes {
				overflow-y: auto;
				-webkit-overflow-scrolling: touch;
				flex-wrap: wrap;
				height: 690rpx;
				width: 690rpx;
				justify-content: flex-start;

				.titles_fl {
					width: 112rpx;
					height: 39rpx;
					font-family: PingFang SC, PingFang SC;
					font-weight: 600;
					font-size: 28rpx;
					color: #999999;
					line-height: 39rpx;
					text-align: left;
					font-style: normal;
					text-transform: none;
				}
			}


		}


	}


	.popup_bq {
		// display: grid;
		// flex-direction: column;
		align-items: center;
		height: 800rpx;
		position: relative;

		img {
			position: absolute;
			width: 750rpx;
			height: 1040rpx;
			top: -164rpx;
			z-index: 100;
		}

		.bqlist {
			// margin-top: 30rpx;
			margin: 0 auto;
			z-index: 200;
			max-height: 100%;

			.selectedBox {
				margin: 10rpx;
				border-radius: 163rpx;
				width: 140rpx auto;
				text-align: center;
				padding: 14rpx 22rpx;
			}

			.allmybqs {
				flex-wrap: wrap;
				width: 690rpx;
			}

			.allbqs {
				overflow-y: auto;
				-webkit-overflow-scrolling: touch;
				flex-wrap: wrap;
				max-height: 100%;
				width: 690rpx;
				justify-content: flex-start;

				.titles_fl {
					width: 112rpx;
					height: 39rpx;
					font-family: PingFang SC, PingFang SC;
					font-weight: 600;
					font-size: 28rpx;
					color: #999999;
					line-height: 39rpx;
					text-align: left;
					font-style: normal;
					text-transform: none;
				}
			}


		}


	}

	.popup_tkallDraft {
		z-index: 100;
		background-color: #f7f7f7;

		.popup_tkDraft {
			font-size: 36rpx;
			margin: 12rpx 0 24rpx 0;
			text-align: center;
		}

		.popup-content {
			height: 760rpx;
			overflow-y: auto;
			margin-top: 30rpx;
			width: 100%;
			padding: 0rpx;
			background-color: #f7f7f7;

			.invoiceList {
				width: 690rpx;
				height: auto;
				// padding: 0rpx 40rpx;

				.invoiceList-item {
					display: flex;
					justify-content: center;
					align-items: center;
					background-color: #ffffff;
					width: 91%;
					margin-top: 20rpx;
					padding: 20rpx 30rpx;
					height: 220rpx;
					border-radius: 40rpx 40rpx 40rpx 40rpx;
					border: 2rpx solid #ffffff;


					.item-img {
						width: 170rpx;
						height: 170rpx;
						// margin-left: 40rpx;

					}

					.item-con {
						margin-left: 30rpx;
						width: 90%;
						height: 160rpx;
						position: relative;
						color: #323232;
						display: grid;

						.itenCon-actName {
							// position: absolute;
							// top: 0;
							font-size: 28rpx;
							font-weight: 600;
						}

						.itenCon-actCon {
							// position: absolute;
							// top: 100rpx;
							// margin-top: 60rpx;
							width: 400rpx;
							white-space: nowrap;
							/* 禁止换行 */
							overflow: hidden;
							/* 隐藏超出部分 */
							text-overflow: ellipsis;
							font-size: 28rpx;
							font-weight: 400;
						}

						.itenCon-actPrice {
							font-size: 26rpx;
							font-weight: 400;
							color: #9c9c9c;
						}
					}


				}

				.invoiceList-itemSelect {
					display: flex;
					justify-content: center;
					align-items: center;
					background-color: #F0FFDF;
					width: 91%;
					margin-top: 20rpx;
					padding: 20rpx 30rpx;
					height: 220rpx;
					border-radius: 40rpx 40rpx 40rpx 40rpx;
					border: 2rpx solid #9CEAA2;
					// border-image: linear-gradient(270deg, rgba(251.00000023841858, 246.0000005364418, 109.00000110268593, 1), rgba(156.00000590085983, 234.00000125169754, 162.00000554323196, 1)) 2 2;

					.item-img {
						width: 170rpx;
						height: 170rpx;
						// margin-left: 40rpx;

					}

					.item-con {
						margin-left: 30rpx;
						width: 90%;
						height: 160rpx;
						position: relative;
						color: #323232;
						display: grid;

						.itenCon-actName {
							// position: absolute;
							// top: 0;
							font-size: 28rpx;
							font-weight: 600;
						}

						.itenCon-actCon {
							// position: absolute;
							// top: 100rpx;
							// margin-top: 60rpx;
							width: 400rpx;
							white-space: nowrap;
							/* 禁止换行 */
							overflow: hidden;
							/* 隐藏超出部分 */
							text-overflow: ellipsis;
							font-size: 28rpx;
							font-weight: 400;
						}

						.itenCon-actPrice {
							color: #9c9c9c;
							font-size: 26rpx;
							font-weight: 400;
						}
					}


				}
			}


			.popup-footer {
				width: 100%;
				display: flex;
				justify-content: center;
				align-items: center;

				.headBtn {
					width: 90%;
					height: 90rpx;
					background-color: #323232;
					border-radius: 148rpx;
					color: #BBFC5B;
					font-size: 36rpx;
					font-weight: 400;
					line-height: 50rpx;
					font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
					text-transform: none;
					font-style: normal;
					display: flex;
					justify-content: center;
					align-items: center;
					text-align: center;
					position: fixed;
					bottom: 66rpx;
					margin-left: 2%;
				}
			}


		}


	}



	.popup_tkall {
		// height: 1100rpx;
		z-index: 100;

		.popup_tk {
			font-size: 36rpx;
			font-weight: 800;
			margin: 12rpx 0 24rpx 0;
			text-align: center;
		}



		.popup-content {
			height: 900rpx;
			overflow-y: auto;
			margin-top: 30rpx;
			width: 100%;

			.popup-footer {
				width: 100%;
				display: flex;
				justify-content: center;
				align-items: center;

				.headBtn {
					width: 90%;
					height: 90rpx;
					background-color: #323232;
					border-radius: 148rpx;
					color: #BBFC5B;
					font-size: 36rpx;
					font-weight: 400;
					line-height: 50rpx;
					font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
					text-transform: none;
					font-style: normal;
					display: flex;
					justify-content: center;
					align-items: center;
					text-align: center;
					position: fixed;
					bottom: 66rpx;
					margin-left: 2%;
				}
			}

		}

		.popup-content-item {
			width: 690rpx;
			padding: 30rpx;
			box-sizing: border-box;
			background: #F7F7F7;
			border-radius: 18rpx;
			border: 5rpx solid #EAEAEA;
			margin-bottom: 30rpx;
			font-family: PingFang SC, PingFang SC;
			justify-content: space-between;
			color: #3D3D3D;

			&.active {
				background: #FAFFF3;
				border: 5rpx solid;
				border-image: linear-gradient(270deg, rgba(251, 246, 109, 1), rgba(156, 234, 162, 1)) 2 2;
				border-radius: 18rpx;
				clip-path: inset(0px round 16rpx);
			}

			.popup-content-item-title {
				font-weight: bold;
				font-size: 36rpx;
			}

			.popup-content-item-content {
				font-weight: 400;
				font-size: 26rpx;
				line-height: 45rpx;

				::v-deep rich-text {
					margin-top: 20rpx;
					display: flex;
				}
			}
		}

		.closetk {
			color: #9C9C9C;
			font-size: 28rpx;
			font-weight: 400;
			font-family: PingFang SC, PingFang SC;
		}

		.confirmtk {
			color: #3D3D3D;
			font-size: 28rpx;
			font-weight: 400;
			font-family: PingFang SC, PingFang SC;
		}
	}

	.btn_1 {
		width: 90%;
		height: 80rpx;
		background: #323232;
		border-radius: 198rpx 198rpx 198rpx 198rpx;
		font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
		font-weight: 400;
		font-size: 32rpx;
		color: #BBFC5B;
		line-height: 80rpx;
		text-align: center;
		margin-top: 40rpx;
		z-index: 100;
	}

	.btn_2 {
		width: 45%;
		height: 80rpx;
		background: #fcc74e;
		border-radius: 198rpx 198rpx 198rpx 198rpx;
		font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
		font-weight: 400;
		font-size: 32rpx;
		color: #333333;
		line-height: 80rpx;
		text-align: center;
		margin-top: 40rpx;
		z-index: 100;
	}

	.btn_3 {
		width: 45%;
		height: 80rpx;
		background: #E2E2E2;
		border-radius: 198rpx 198rpx 198rpx 198rpx;
		font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
		font-weight: 400;
		font-size: 32rpx;
		color: #999999;
		line-height: 80rpx;
		text-align: center;
		margin-top: 40rpx;
		z-index: 100;
	}

	.btn_4 {
		width: 50%;
		height: 80rpx;
		background: #ffffff;
		border: 1px solid #999999;
		border-radius: 198rpx 198rpx 198rpx 198rpx;
		font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
		font-weight: 400;
		font-size: 32rpx;
		color: #999999;
		line-height: 80rpx;
		text-align: center;
		margin-top: 40rpx;
		z-index: 100;
	}
</style>