<!-- 优惠券中心  -->
<template>
	<s-layout title="我的优惠券">
		<view class="container">
			<!-- tabs页签 -->
			<view class="tabs-box">
				<su-tabs :list="tabMaps" @change="onChange" :scrollable="false" :current="currentTab"></su-tabs>
			</view>

			<!-- 可兑换券列表 -->
			<scroll-view @scrolltolower="onScrolltolower" :refresher-enabled="true" :refresher-triggered="homrS"
				@refresherrefresh="onS" scroll-y="true" class="flex align-items"
				style="height: 83vh;box-sizing: border-box;margin-top: 30rpx;">
				<!-- 满减券 -->
				<view class="exChangeBox" v-if="currentTab == 0">
					<view class="couponsListFull">
						<view class="fullItem" v-for="(item, index) in couponFullList" :key="index">
							<view class="itemFir">
								<view class="itemFirLeft">
									<view class="fslh42 c3 bold" style="line-height: 36rpx;">￥{{item.amount}}</view>
									<view class="fs28 c3D regular"
										style="margin-top: 20rpx;line-height: 39rpx;width: 440rpx;">{{item.name}}
									</view>
								</view>
								<view class="item-btn" @click="toUseFull(item)">去使用</view>
							</view>
							<view class="line"></view>
							<view class="itemSec">
								<image style="width: 109rpx;height: 46rpx;"
									src="https://jiangxiaoxian.0rui.cn/couponFull.png"></image>
								<view class="fs24lh30 c9 regular" style="margin-left: 18rpx;">仅限原料商城可用</view>
							</view>
						</view>
					</view>

				</view>
				<!-- 次数券 -->
				<view class="exChangeBox" v-if="currentTab == 1">
					<view class="couponsList" v-for="(e, i) in couponTimesList" :key="i">
						<view class="list-item">
							<view class="itemLeft">
								<view class="fslh60 c3 bold" style="text-align: center;">{{e.mobile_number}}</view>
								<view class="fslh28 c3 regular" style="margin-top: 15rpx;">次数券</view>
							</view>
							<view class="itemRight">
								<view class="item-RightLeft">
									<view class="fs32 c3D bold" style="line-height: 36rpx;">{{e.name}}</view>
									<view class="fs24 c9 regular" style="margin-top: 30rpx;line-height: 30rpx;">
										仅限增加拨打次数使用</view>
								</view>
								<view class="item-btn" @click="toUseTimes(e)">去使用</view>
							</view>

						</view>
					</view>
				</view>

				<!-- 无数据展示 -->
				<view class="grid flex-column justify-center align-center"
					v-if="(currentTab == 0 && couponFullCount == 0) || (currentTab == 1 && couponTimesCount == 0)"
					style="margin: 0 auto;margin-top: 300rpx;">
					<image src="https://jiangxiaoxian.0rui.cn/noneList.png" mode=""
						style="width: 520rpx;height: 259rpx;">
					</image>
					<view style="margin-top: 30rpx;font-size: 28rpx;color: #323232;text-align: center;width: 100%;">
						暂无数据信息</view>
				</view>

			</scroll-view>


		</view>

	</s-layout>
</template>

<script setup>
	import sheep from '@/sheep';
	import {
		onLoad,
		onShow,
		onReachBottom
	} from '@dcloudio/uni-app';
	import {
		ref,
		reactive
	} from 'vue';
	import _ from 'lodash';

	const listQuery = ref({
		page: 1,
		limit: 10,
		list_rows: 10,
		status: 1,
	})
	const tabMaps = [{
			name: '满减券',
			value: '1',
		},
		{
			name: '次数券',
			value: '2',
		},
	];
	const currentTab = ref(0)
	const score = ref(0)
	//切换tabs
	function onChange(e) {
		console.log('onChange', e);
		currentTab.value = e.index
		console.log('切换tabs', currentTab.value);
		listQuery.value.status = e.value
		listQuery.value.page = 1;
		listQuery.value.limit = 10;
		listQuery.value.list_rows = 10
		couponFullList.value = []
		couponFullCount.value = 0;
		couponTimesList.value = [];
		couponTimesCount.value = 0;
		if (currentTab.value == 0) {
			getCouponFullList();
		} else {
			getCouponTimesList();
		}
	}

	//页面跳转
	const toPage = (e) => {
		uni.navigateTo({
			url: e
		})

	}

	/* 满减券 */
	const couponFullList = ref([])
	const couponFullCount = ref(0)
	const homrS = ref(false)
	async function getCouponFullList() {
		const res = await sheep.$api.coupon.userCoupon({
			page: listQuery.value.page,
			list_rows: listQuery.value.list_rows,
			add_type: 'reduce',
			type: 'geted'
		});
		if (res.code === 1) {
			couponFullList.value = [...couponFullList.value, ...res.data.data];
			couponFullCount.value = res.data.total;
			console.log('满减券', couponFullList.value, couponFullList.value[0]);
		} else {
			uni.showToast({
				title: res.msg,
				icon: 'none'
			})
		}
	}
	/* 次数券 */
	const couponTimesList = ref([])
	const couponTimesCount = ref(0)
	async function getCouponTimesList() {
		const res = await sheep.$api.coupon.userTimesList({
			page: listQuery.value.page,
			limit: listQuery.value.limit,
			status: 1,
			order: 'normal'
		});
		if (res.code === 1) {
			couponTimesList.value = [...couponTimesList.value, ...res.data.list];
			couponTimesCount.value = res.data.count;
			console.log('couponTimesList', couponTimesList.value);
			// score.value = res.data.score
		} else {
			uni.showToast({
				title: res.msg,
				icon: 'none'
			})
		}
	}

	/* 使用券 */
	function toUseFull(item) {
		console.log('使用满减券', item);
		uni.navigateTo({
			url: '/pages/index/category'
		})
	}
	async function toUseTimes(e) {
		console.log('使用次数券', e);
		const data = {
			id: e.id
		}
		const res = await sheep.$api.coupon.useTimes(data)
		console.log('使用次数券_res:', res);
		if (res.code == 1) {
			uni.showToast({
				title: '已增加您的拨打号码次数',
				icon: 'success',
				duration: 2000,
			})
			setTimeout(() => {
				couponTimesList.value = [];
				getCouponTimesList();
			},1500)
			
		} else {
			uni.showToast({
				title: res.msg,
				icon: 'error'
			})
		}

	}

	//加载更多
	function onScrolltolower() {
		if (currentTab.value == 0) {
			//满减券
			if (couponFullList.value.length < couponFullCount.value) {
				listQuery.value.page += 1;
				getCouponFullList();
			}
		} else {
			//次数券
			if (couponTimesList.value.length < couponTimesCount.value) {
				listQuery.value.page += 1;
				getCouponTimesList();
			}
		}

	}
	//下拉刷新
	function onS() {
		homrS.value = true
		listQuery.value.keywords = ''
		uni.showLoading({
			title: '加载中...'
		});
		resetLists();
		setTimeout(() => {
			homrS.value = false;
			uni.hideLoading();
			uni.stopPullDownRefresh();
		}, 2000)
	}
	// 重置列表
	function resetLists() {
		listQuery.value.page = 1;
		couponFullList.value = [];
		// couponFullCount.value = 0;
		// couponTimesCount.value = 0
		couponTimesList.value = [];
		getCouponFullList();
		getCouponTimesList();
		// getCouponList();
	}

	onShow(() => {
		couponFullList.value = []
		couponTimesList.value = []
		getCouponFullList();
	});
</script>


<style lang="scss" scoped>
	.fslh60 {
		font-size: 60rpx;
		line-height: 60rpx;
	}

	.fslh42 {
		font-size: 42rpx;
		line-height: 42rpx;
	}

	.fslh28 {
		font-size: 28rpx;
		line-height: 28rpx;
	}

	.fs24lh30 {
		font-size: 24rpx;
		line-height: 30rpx;
	}

	.fs32 {
		font-size: 32rpx;
	}

	.fs28 {
		font-size: 28rpx;
	}

	.justify-center {
		justify-content: center;
	}

	.align-center {
		align-items: center;
	}

	.flex {
		display: flex;
	}

	.grid {
		display: grid;
	}

	.fs60 {
		font-size: 60rpx;
	}

	.fs24 {
		font-size: 24rpx;
	}

	.bold {
		font-weight: bold;
	}

	.regular {
		font-weight: 400;
	}

	.c3 {
		color: #333333;
	}

	.c3D {
		color: #3d3d3d;
	}

	.c9 {
		color: #999999;
	}
	

	.container {
		background-color: #f7f7f7;

		.exChangeBox {
			// height: 80vh;
			// background-color: #f7f7f7;
			// width: 690rpx;

			.couponsListFull {
				// width: 690rpx;
				padding: 0 30rpx;
				// display: grid;
				justify-content: center;
				align-items: center;
				min-height: 80vh;

				.fullItem {
					width: 690rpx;
					background-color: #ffffff;
					border-radius: 20rpx;
					display: grid;
					justify-content: flex-start;
					align-items: center;
					// padding: 30rpx;
					margin-bottom: 30rpx;

					.itemFir {
						display: flex;
						justify-content: space-between;
						align-items: center;
						margin: 30rpx;

						.item-btn {
							width: 160rpx;
							height: 70rpx;
							background: linear-gradient(to right, #FCCA58, #FFBD25);
							border-radius: 106rpx;
							align-items: center;
							justify-content: center;
							display: flex;
							font-size: 28rpx;
							line-height: 36rpx;
							font-weight: bold;
							color: #333333;
						}
					}

					.line {
						width: 690rpx;
						height: 1rpx;
						background-color: #eeeeee;
						// margin-top: 30rpx;
						// margin-bottom: 20rpx;
					}

					.itemSec {
						margin: 30rpx;
						display: flex;
						justify-content: flex-start;
						align-items: center;
					}
				}

			}

			.couponsList {
				width: 100%;
				// min-height: 50vh;
				padding: 0 30rpx;
				display: grid;
				grid-template-columns: repeat(2, 1fr);
				;
				grid-gap: 20px;
				// display: flex;
				// justify-content: center;
				// align-items: center;


				.list-item {
					width: 690rpx;
					background-image: url('https://jiangxiaoxian.0rui.cn/couponItemBack.png');
					background-size: 100%;
					display: flex;
					margin-bottom: 30rpx;
					justify-content: space-between;

					.itemLeft {
						width: 84rpx;
						height: 103rpx;
						padding: 40rpx 50rpx;
						display: block;
						justify-content: center;
						align-items: center;
					}

					.itemRight {
						width: 446rpx;
						height: 103rpx;
						padding: 40rpx 30rpx;
						display: flex;
						justify-content: space-between;
						align-items: center;

						.item-RightLeft {
							// width: 84rpx;
							// height: 103rpx;
							// padding: 40rpx 50rpx;
							display: block;
							justify-content: flex-start;
							align-items: center;
						}

						.item-btn {
							width: 160rpx;
							height: 70rpx;
							background: linear-gradient(to right, #FCCA58, #FFBD25);
							border-radius: 106rpx;
							align-items: center;
							justify-content: center;
							display: flex;
							font-size: 28rpx;
							line-height: 36rpx;
							font-weight: bold;
							color: #333333;
						}

					}


					.item-text {
						padding: 30rpx;

						.exchangeBtn {
							background-color: #fcc74e;
							height: 70rpx;
							width: 270rpx;
							margin-top: 30rpx;
							border-radius: 223rpx;
							align-items: center;
							justify-content: center;
							display: flex;
							font-size: 28rpx;
							line-height: 30rpx;
							font-weight: 400;
						}
					}
				}

			}

		}

	}
</style>