<template>
	<s-layout title="认证信息" :bgStyle="{ color: '#ffffff' }">
		<view class="container">
			<view class="contifionType">
				<su-tabs :list="tabMaps" @change="onChange" :scrollable="false" :current="currentTab"></su-tabs>
			</view>

			<view style="padding: 0 30rpx;width: 100%;">
				<view v-if="currentTab == 0">
					<image v-if="form.workimage != '' && form.status == 1" style="width: 690rpx;height: 350rpx;" :src="baseUrl + form.workimage" @click="afterRead"></image>
					<image v-else style="width: 690rpx;height: 350rpx;"
						src="https://jiangxiaoxian.0rui.cn/manager.png" @click="afterRead"></image>
				</view>

				<view v-if="currentTab == 1">
					<image v-if="form.gateimage != '' && form.status == 2" style="width: 690rpx;height: 350rpx;" 
					:src="baseUrl + form.gateimage" @click="afterRead"></image>
					<image v-else style="width: 690rpx;height: 350rpx;" @click="afterRead"
					src="https://jiangxiaoxian.0rui.cn/Merchant.png">
					</image>
					
				</view>
			</view>


			<view class="footer-box">
				<view class="submitAuth" @click="submit()">提交认证</view>
			</view>

		</view>
	</s-layout>
</template>

<script setup>
	import sheep from '@/sheep';
	import {
		onLoad,
		onShow,
		onReachBottom
	} from '@dcloudio/uni-app';
	import {
		ref,
		reactive
	} from 'vue';
	import _ from 'lodash';
	import {
		baseUrl
	} from '@/sheep/config';

	const tabMaps = [{
			name: '餐厅经理',
			value: '1',
		},
		{
			name: '档口商户',
			value: '2',
		},
	];
	const currentTab = ref(0)
	//切换tabs
	function onChange(e) {
		console.log('onChange', e);
		currentTab.value = e.index
		console.log('切换tabs', currentTab.value);
		if (currentTab.value == 0) {
			form.value.status = 1
			form.value.workimage = '';
			form.value.gateimage = '';
		} else {
			form.value.status = 2
			form.value.gateimage = '';
			form.value.workimage = '';
		}
	}

	const form = ref({
		workimage: '',
		gateimage: '',
		status: 0,
	})

	async function uploadAvatar(tempUrl) {
		if (!tempUrl) return;
		let {
			url
		} = await sheep.$api.app.upload(tempUrl, 'ugc');
		console.log('url', url);
		if (currentTab.value == 0) {
			form.value.status = 1
			form.value.workimage = url;
		} else {
			form.value.status = 2
			form.value.gateimage = url;
		}
	}

	async function afterRead() {
		console.log('afterRead触发了吗？');
		uni.chooseImage({
			success: async (chooseImageRes) => {
				const tempUrl = chooseImageRes.tempFilePaths[0];
				uploadAvatar(tempUrl);
			},
		});
	}
	
	function submit() {
		const data = {
			status: form.value.status,
			workimage: form.value.workimage,
			gateimage: form.value.gateimage,
		}
		sheep.$api.user.authPhoto(data).then((res) => {
			if(res.code == 1) {
				console.log('提交认证成功');
				uni.showToast({
					title:'提交认证成功',
					icon: 'success',
				})
				
				setTimeout(() => {
					uni.navigateBack()
				},1000)
			}
		})
		
	}
	
</script>

<style lang="scss" scoped>
	.container {
		background: #ffffff;
		width: 100%;
		// display: grid;
		// justify-content: center;


		.contifionType {
			margin-bottom: 30rpx;
		}


		.footer-box {
			width: 100%;
			height: 90rpx;
			padding: 30rpx;
			position: fixed;
			bottom: 10rpx;
			left: 0;

			.submitAuth {
				background: linear-gradient(to right, #FFBD25, #FCCA58);
				width: 690rpx;
				height: 90rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				color: #333333;
				font-size: 32rpx;
				font-weight: 45rpx;
				font-weight: bold;
				border-radius: 148rpx;
			}
		}

	}
</style>