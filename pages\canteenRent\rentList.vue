<template>
	<s-layout title="食堂招租">
		<view class="container">
			<!-- 顶部 -->
			<view class="top">
				<!-- 顶部-地址&搜索 -->
				<view class="locTop">
					<view class="locTop-right">
						<uni-section style="border-radius: 192rpx;padding: 0;" type="line">
							<uni-search-bar v-model="listQuery.keywords" style="border-radius: 192rpx;" radius="23"
								placeholder="搜索您需要的信息" bgColor="#EEEEEE" clearButton="none" cancelButton="none"
								@confirm="searchRent" />
						</uni-section>
						<!-- @blur="searchRent1"-->
					</view>
					<view class="locTop-left">
						<view class="locTop-left-left" @click="state.showRegion = true">
							<view @click="state.showRegion = true"
								style="font-size: 28rpx;font-weight: 400;line-height: 32rpx;color: #999999;max-width: 140rpx;">
								{{state.model.city_name ? state.model.city_name:'全国' }}
							</view>
							<image @click="state.showRegion = true"
								style="width: 27rpx;height: 27rpx;margin-left: 10rpx;"
								src="https://jiangxiaoxian.0rui.cn/rentMore.png" mode=""></image>
						</view>
						<picker mode="selector" :value="tagList.id" :range="tagList" range-key="name"
							@change="onChangeTag">
							<view class="picker"
								style="width: auto; position: relative;display: flex;justify-content: flex-start;align-items: center;">
								<view
									style="max-width: 110rpx; position: relative;font-size: 28rpx;font-weight: 400;color: #999999;line-height: 32rpx;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;">
									{{dictLabel || '标签'}}
								</view>
								<image style="width: 27rpx;height: 27rpx;margin-left: 6rpx;"
									src="https://jiangxiaoxian.0rui.cn/rentMore.png" mode=""></image>
							</view>
						</picker>


					</view>

				</view>


			</view>

			<!-- 推荐列表 -->
			<scroll-view @scrolltolower="onScrolltolower" :refresher-enabled="true" :refresher-triggered="homrS"
				@refresherrefresh="onS" scroll-y="true" class="flex align-items"
				style="height: 1250rpx;box-sizing: border-box;">
				<view class="recommend-box">
					<view class="rrecommList">
						<view class="reListItem" v-for="(item,index) in rentList" :key="index" @click="toDetail(item)">
							<!-- 左侧图片 -->
							<view class="listItem-images">
								<image v-if="item.recommend == 1"
									style="width: 77rpx;height: 36rpx;z-index: 100;position: relative;top: 34rpx;left: 0rpx;"
									src="https://jiangxiaoxian.0rui.cn/hot.png"></image>
								<image v-if="item.recommend != 1"
									style="width: 77rpx;height: 36rpx;z-index: 100;position: relative;top: 34rpx;left: 0rpx;"
									src=""></image>
								<image class="iamges-mainImg" :src="item.images[0]" mode="aspectFill"></image>
							</view>
							<!-- 右侧内容 -->
							<view class="listItem-contents">
								<view class="title2" style="margin-bottom: 20rpx;">{{item.platform_title}}</view>
								<scroll-view class="score-box">
									<view class="fs11 flex cate">
										<view class="cate-e" v-for="(e,i) in item.cate" :key="i">
											<view v-show="e" class="status-tag " style="margin-right: 20rpx;">{{e}}
											</view>
										</view>
									</view>
								</scroll-view>
								<view class="cons-third">
									<view style="display: flex;">
										<image style="width: 24rpx;height: 24rpx;"
											src="https://jiangxiaoxian.0rui.cn/locationList.png"></image>
										<view class="title3" style="margin-left: 6rpx;">{{item.address_city_text}}
										</view>
									</view>
									<view class="title3">{{item.release_time_text}}</view>
									<view style="display: flex;align-items: center;justify-content: center;">
										<image style="width: 26rpx;height: 20rpx;"
											src="https://jiangxiaoxian.0rui.cn/eye.png"></image>
										<view class="title3" style="margin-left: 6rpx;">{{item.views}}</view>
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>

			</scroll-view>

		</view>

		<!-- 省市区弹窗 -->
		<su-regionCity-picker :show="state.showRegion" @cancel="state.showRegion = false" @confirm="onRegionConfirm">
		</su-regionCity-picker>

	</s-layout>
</template>

<script setup>
	import {
		computed,
		ref,
		reactive,
	} from 'vue';
	import {
		onLoad,
		onShow,
		onPageScroll,
		onPullDownRefresh,
		onReachBottom
	} from '@dcloudio/uni-app';
	import {
		consignee,
		mobile,
		address,
		region
	} from '@/sheep/validate/form';
	import rent from '../../sheep/api/rent';
	import sheep from '@/sheep';

	const listQuery = ref({
		page: 1,
		limit: 10,
		addrLatitude: null,
		addrLongitude: null,
		address: null,
		address1: null,
		categoryId: null,
		tags: '',
		cate_ids: '',
		area: null,
		city: null,
		province: null,
		district: null,
		keywords: null,

		// district: '',
	})
	const dictLabel = ref(null)
	const tagList = ref([])

	onLoad(() => {
		getList();
		getTagList()
	})

	onShow(() => {})

	const state = reactive({
		showRegion: false,
		model: {
			consignee: '',
			mobile: '',
			address: '',
			is_default: false,
			region: '',
		},
		rules: {
			consignee,
			mobile,
			address,
			region,
		},
		loadStatus: '',
		type: '',
	});

	const proCity = ref(''); //省市页面显示
	function searchRent() {
		rentList.value = []
		console.log('搜索', listQuery.value.keywords);
		getList()
	}

	const onRegionConfirm = (e) => {
		console.log('onRegionConfirm', e);

		state.model = {
			...state.model,
			...e,
		};
		// proCity.value = state.model.province_name + " " + state.model.city_name

		console.log('onRegionConfirm33', state.model, proCity.value);
		listQuery.value.province = state.model.province_id
		listQuery.value.city = state.model.city_id
		rentList.value = []
		getList()
		state.showRegion = false;
	};

	/* 标签 */
	const tagShowList = ref([])
	async function getTagList() {
		const obj = [{
			name: '全部',
			id: ''
		}]
		const res = await sheep.$api.rent.tagsList({});
		if (res.code == 1) {
			tagList.value = obj.concat(res.data.list)
			tagShowList.value = tagList.value.map(({
				name,
				id
			}) => ({
				name,
				id
			}));
			console.log('tagList', tagList.value, tagShowList.value);
		}
	}

	/**选择标签 */
	function onChangeTag(e) {
		const value = e.detail.value;
		listQuery.value.cate_ids = tagList.value[e.detail.value].id;
		dictLabel.value = tagList.value[e.detail.value].name;
		console.log('标签', listQuery.value);
		rentList.value = []
		getList(listQuery.value);

	}

	const rentList = ref([])
	const rentCount = ref(0)
	//招租列表
	async function getList() {
		const res = await sheep.$api.rent.rentlist({
			keywords: listQuery.value.keywords,
			page: listQuery.value.page,
			limit: listQuery.value.limit,
			cate_ids: listQuery.value.cate_ids,
			order: 'normal',
			status: 1,
			province: listQuery.value.province,
			city: listQuery.value.city,
		});
		console.log('getList', res);

		if (res.data && res.data.list) {
			// rentList.value = res.data.list;
			rentList.value = [...rentList.value, ...res.data.list];
			rentCount.value = res.data.count;
			// Safely process each item
			rentList.value = rentList.value.map(item => {
				// Create a new object with all properties from the original item
				const newItem = {
					...item
				};

				// Only process release_time_text if it exists
				if (newItem.release_time_text) {
					newItem.release_time_text = newItem.release_time_text.substring(0, 10);
				}

				return newItem;
			});
		} else {
			// Handle case where data is not in expected format
			rentList.value = [];
		}

		// rentList.value = res.data.list;
		// for(let i=0;i<rentList.value.length;i++){
		// 	rentList.value[i].release_time_text = rentList.value[i].release_time_text.substring(0, 10)
		// }
		console.log('getList', rentList.value, rentCount.value);
	}
	//加载更多
	function onScrolltolower() {
		if (rentList.value.length < rentCount.value) {
			listQuery.value.page += 1;
			getList();
		}
	}
	//下拉刷新
	function onS() {
		homrS.value = true
		listQuery.value.keywords = ''
		uni.showLoading({
			title: '加载中...'
		});
		resetLists();

		setTimeout(() => {
			homrS.value = false;
			uni.hideLoading();
			uni.stopPullDownRefresh();
		}, 2000)
	}
	// 重置列表
	function resetLists() {
		listQuery.value.page = 1;
		rentList.value = [];
		getList();
	}

	function toDetail(e) {
		console.log('跳转详情', e);
		uni.navigateTo({
			url: '/pages/canteenRent/rentDetail?id=' + e.id
		})
	}
</script>

<style lang="scss" scoped>
	.popupContacted {
		width: 660rpx;
		height: 477rpx;
		background-color: #fff;
		display: grid;
		border-radius: 18rpx;

		.contactedBox {
			width: 660rpx;
			height: 325rpx;
			padding: 30rpx 0;
			// margin-top: 40rpx;
			display: grid;
			justify-content: center;
			align-items: center;
		}

		.contactBtn {
			width: 660rpx;
			height: 92rpx;
			border-top: 1rpx solid #eeeeee;
			display: flex;
			border-radius: 0 0 18rpx 18rpx;
			// margin-top: 40rpx;

			.tactBtnBox {
				width: 330rpx;
				height: 92rpx;
				border-right: 1rpx solid #eeeeee;
				display: flex;
				align-items: center;
				justify-content: center;
			}

			.tactBtnBox2 {
				width: 330rpx;
				height: 92rpx;
				// border-right: 1rpx solid #eeeeee;
				display: flex;
				align-items: center;
				justify-content: center;
			}
		}

	}

	.search-result {
		padding-top: 10px;
		padding-bottom: 20px;
		text-align: center;
	}

	.search-result-text {
		text-align: center;
		font-size: 14px;
		color: #666;
	}

	.example-body {
		/* #ifndef APP-NVUE */
		display: block;
		/* #endif */
		padding: 0px;
	}

	.uni-mt-10 {
		margin-top: 10px;
	}

	.title2 {
		color: #3d3d3d;
		font-size: 30rpx;
		font-weight: 800;
		line-height: 42rpx;
	}

	.title3 {
		font-size: 20rpx;
		font-weight: 400;
		color: #999999;
		line-height: 22rpx;
	}

	.container {
		width: 100%;
		height: 100%;

		.top {
			width: 100%;
			height: 80rpx;
			background-color: #ffffff;
			// background-image: url('https://jiangxiaoxian.0rui.cn/topBack.png');
			background-size: 100%;
			padding: 20rpx 30rpx;
			display: grid;

			.locTop {
				width: 96%;
				height: 70rpx;
				display: flex;
				// justify-content: space-between;
				align-items: center;

				.locTop-left {
					position: absolute;
					right: 3%;
					width: 200rpx auto;
					display: flex;
					align-items: center;
					justify-content: space-between;
					margin-right: 20rpx;

					.locTop-left-left {
						width: 176rpx auto;
						display: flex;
						align-items: center;
						justify-content: flex-start;
						margin-right: 20rpx;
					}

				}

				.locTop-right {
					width: 350rpx;
					height: 70rpx;

				}
			}

		}

		.recommend-box {
			margin-top: 30rpx;
			padding: 0rpx 30rpx;

			.rrecommList {
				// margin: 0 auto;
				// margin-top: 30rpx;
				// padding: 30rpx;

				.reListItem {
					background-color: #ffffff;
					width: 640rpx;
					height: 190rpx;
					padding: 30rpx;
					display: flex;
					margin-top: 20rpx;
					justify-content: flex-start;
					align-items: center;
					border-radius: 18rpx;

					.listItem-images {
						display: grid;
						justify-content: flex-start;

						.iamges-mainImg {
							width: 190rpx;
							height: 190rpx;
							position: relative;
							right: 0rpx;
							bottom: 18rpx;
							z-index: 1;
							border-radius: 12rpx;

						}
					}

					.listItem-contents {
						margin-left: 20rpx;

						.score-box {
							display: flex;

							.cate {
								display: flex;
								justify-content: flex-start;
								align-items: center;

								.cate-e {

									.status-tag {
										padding: 5rpx 15rpx;
										border-radius: 5rpx;
										font-size: 22rpx;
										font-weight: 400;
										line-height: 22rpx;
										background-color: rgba(247, 97, 0, 0.2);

										// opacity: 0.2;
										color: #F76100;
										//flex-shrink: 0
									}
								}
							}



						}



						.cons-third {
							margin-top: 20rpx;
							width: 430rpx;
							display: flex;
							justify-content: space-between;
							align-items: center;
						}
					}
				}

			}

		}

	}
</style>