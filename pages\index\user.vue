<template>
	<s-layout title="我的" tabbar="/pages/index/user" navbar="custom" :bgStyle="template.style?.background"
		:navbarStyle="template.style?.navbar" onShareAppMessage :showFloatButton="true">
		<!-- 登录信息 -->
		<view class="myBox">
			<view @touchmove.prevent scroll-y="true" :show-scrollbar="true"
				class="con-center flex flex-start flex-column">
				<view class="header">
					<!-- 用户信息 -->
					<view class="hea-left flex">
						<view class="head-avatar" v-if="isLogin == true" @click="sheep.$router.go('/pages/user/info')">
							<image style="width: 130rpx;height: 130rpx;border-radius: 152rpx;" :src="baseUrl + userInfo.avatar"></image>
						</view>
						<view class="head-avatar" v-else @click="toLogin">
							<image style="width: 130rpx;height: 130rpx;border-radius: 152rpx;" src="/static/wechat.png">
							</image>
						</view>
						<view class="headAvatar-info justify-start">
							<view class="info-name fs-36 weight8 flexD" v-if="isLogin == true">
								<view style="font-weight: 800;" @click="sheep.$router.go('/pages/user/info')">{{ userInfo.nickname}} </view>
								<view class="UnAuthenBox weight8 fs-20" v-if="authenInfo.status == -1 " @click="toPage('/pages/user/authentication')">未认证</view>
								<view class="authenticationBox weight8 fs-20" v-if="authenInfo.status == 1 ">已认证</view>
							</view>
							<view class="info-name fs-36 weight8 flexD" v-else>
								<view style="font-weight: 800;" @click="toLogin">登录/注册</view>
								<!-- <view class="UnAuthenBox weight8 fs-20" v-if="authenInfo.status == -1 && isLogin == true">未认证</view>
								<view class="authenticationBox weight8 fs-20" v-if="authenInfo.status == 1 && isLogin == true">已认证</view> -->
							</view>
							<view class="fs-28 weight4" style="margin-top: 30rpx;">剩余拨打次数： {{ userInfo.all_mobile_num ? userInfo.all_mobile_num : 0 }}次</view>
						</view>
						<view class="head-signInBox" @click="toPage('/pages/app/sign')">
							<image style="width: 46rpx;height: 44rpx;" src="https://jiangxiaoxian.0rui.cn/signIn.png"
								mode=""></image>
							<view class="signInText" style="margin-left: 10rpx;">签到</view>
						</view>

					</view>

					<!-- 四个主内容 -->
					<view class="head-secondBox">
						<view class="collect" @click="toPage('/pages/user/goods-collect')">
							<view class="collect-num">{{ userInfo.collect_number ? userInfo.collect_number : 0 }}</view>
							<view class="collect-text">收藏</view>
						</view>
						<view class="feetPrint" @click="toPage('/pages/user/goods-log')">
							<view class="feetPrint-num">{{ userInfo.footprint_number ? userInfo.footprint_number : 0 }}</view>
							<view class="feetPrint-text">足迹</view>
						</view>
						<view class="feetPrint" @click="toPage('/pages/user/wallet/score')">
							<view class="feetPrint-num">{{ userInfo.score ? userInfo.score : 0 }}</view>
							<view class="feetPrint-text">积分</view>
						</view>
						<view class="collect" @click="toPage('/pages/coupon/list')">
							<view class="collect-num">{{ userInfo.coupon_number ? userInfo.coupon_number : 0 }}</view>
							<view class="collect-text">优惠券</view>
						</view>
					</view>
				</view>

				<!-- 我的订单 -->
				<view class="myOrder">
					<view class="myOrder-first" @click="toPage('/pages/order/list')">
						<view style="font-weight: 800;font-size: 34rpx;line-height: 28rpx;color: #3d3d3d;">我的订单</view>
						<view style="display: flex;align-items: center;justify-content: center;">
							<view style="font-size: 26rpx;line-height: 26rpx;font-weight: 400;color: #999999;">全部订单
							</view>
							<image style="width: 28rpx;height: 28rpx;margin-left: 10rpx;"
								src="https://jiangxiaoxian.0rui.cn/rightMore.png" mode=""></image>
							<!-- <uni-icons type="arrowthinright" color="#999" style="line-height: 40px;"></uni-icons> -->
						</view>
					</view>
					<view class="myOrder-second">
						<view class="pendingBox" @click="toPage('/pages/order/list?type=1')">
							<image class="pendImg" src="https://jiangxiaoxian.0rui.cn/pendPay.png"></image>
							<view class="fs-28 weight4" style="margin-top: 20rpx;color: #3d3d3d;">待支付</view>
						</view>
						<view class="pendingBox" @click="toPage('/pages/order/list?type=2')">
							<image class="pendImg" src="https://jiangxiaoxian.0rui.cn/shipment.png"></image>
							<view class="fs-28 weight4" style="margin-top: 20rpx;color: #3d3d3d;">待发货</view>
						</view>
						<view class="pendingBox" @click="toPage('/pages/order/list?type=3')">
							<image class="pendImg" src="https://jiangxiaoxian.0rui.cn/pendingDoods.png"></image>
							<view class="fs-28 weight4" style="margin-top: 20rpx;color: #3d3d3d;">待收货</view>
						</view>
						<view class="pendingBox" @click="toPage('/pages/order/list?type=4')">
							<image class="pendImg" src="https://jiangxiaoxian.0rui.cn/completed.png"></image>
							<view class="fs-28 weight4" style="margin-top: 20rpx;color: #3d3d3d;">已完成</view>
						</view>
					</view>
				</view>

				<!-- 发布分享等 -->
				<view class="sixBox">
					<!-- 我的发布 -->
					<view class="fabuBox" @click="toPage('/pages/user/myRelease/list')">
						<view class="flex align-items" >
							<image style="width: 36rpx;height: 36rpx;margin-right: 20rpx;"
								src="https://jiangxiaoxian.0rui.cn/myRelease.png"></image>
							<view style="font-size: 30rpx;font-weight: 800;line-height: 28rpx;color: #333333;">我的发布
							</view>
						</view>
						<image style="width: 28rpx;height: 28rpx;margin-left: 10rpx;"
							src="https://jiangxiaoxian.0rui.cn/rightMore.png" mode=""></image>
					</view>
					<view class="line-row" style="margin: 30rpx 0;"></view>
					<!-- 积分商城 -->
					<view class="fabuBox" @click="toPage('/pages/app/score-shop')">
						<view class="flex align-items">
							<image style="width: 36rpx;height: 36rpx;margin-right: 20rpx;"
								src="https://jiangxiaoxian.0rui.cn/points.png"></image>
							<view style="font-size: 30rpx;font-weight: 800;line-height: 28rpx;color: #333333;">积分商城
							</view>
						</view>
						<image style="width: 28rpx;height: 28rpx;margin-left: 10rpx;"
							src="https://jiangxiaoxian.0rui.cn/rightMore.png" mode=""></image>
					</view>
					<view class="line-row" style="margin: 30rpx 0;"></view>
					<!-- 分享好友 -->
					<view class="fabuBox" @click="showShareModal">
						<view class="flex align-items">
							<image style="width: 36rpx;height: 36rpx;margin-right: 20rpx;"
								src="https://jiangxiaoxian.0rui.cn/share.png"></image>
							<view style="font-size: 30rpx;font-weight: 800;line-height: 28rpx;color: #333333;">分享好友
							</view>
						</view>
						<image style="width: 28rpx;height: 28rpx;margin-left: 10rpx;"
							src="https://jiangxiaoxian.0rui.cn/rightMore.png" mode=""></image>
					</view>
					<view class="line-row" style="margin: 30rpx 0;"></view>
					<!-- 官方客服 -->
					<view class="fabuBox" @click="toPage('/pages/chat/index')">
						<view class="flex align-items">
							<image style="width: 36rpx;height: 36rpx;margin-right: 20rpx;"
								src="https://jiangxiaoxian.0rui.cn/service.png"></image>
							<view style="font-size: 30rpx;font-weight: 800;line-height: 28rpx;color: #333333;">官方客服
							</view>
						</view>
						<image style="width: 28rpx;height: 28rpx;margin-left: 10rpx;"
							src="https://jiangxiaoxian.0rui.cn/rightMore.png" mode=""></image>
					</view>
					<view class="line-row" style="margin: 30rpx 0;"></view>
					<!-- 意见反馈 -->
					<view class="fabuBox" @click="toPage('/pages/public/feedback')">
						<view class="flex align-items">
							<image style="width: 36rpx;height: 36rpx;margin-right: 20rpx;"
								src="https://jiangxiaoxian.0rui.cn/feedBack.png"></image>
							<view style="font-size: 30rpx;font-weight: 800;line-height: 28rpx;color: #333333;">意见反馈
							</view>
						</view>
						<image style="width: 28rpx;height: 28rpx;margin-left: 10rpx;"
							src="https://jiangxiaoxian.0rui.cn/rightMore.png" mode=""></image>
					</view>
					<view class="line-row" style="margin: 30rpx 0;"></view>
					<!-- 设置 -->
					<view class="fabuBox" @click="toSetting">
						<view class="flex align-items">
							<image style="width: 36rpx;height: 36rpx;margin-right: 20rpx;"
								src="https://jiangxiaoxian.0rui.cn/setUp.png"></image>
							<view style="font-size: 30rpx;font-weight: 800;line-height: 28rpx;color: #333333;" >设置</view>
						</view>
						<image style="width: 28rpx;height: 28rpx;margin-left: 10rpx;"
							src="https://jiangxiaoxian.0rui.cn/rightMore.png" mode=""></image>
					</view>

				</view>


				<view style="height: 180rpx;width: 100%;"></view>
			</view>

			<su-popup :show="showLogin == true" type="center" round="10" :isMaskClick="false" >
				<view class="popup">
					
					<button class="texbut" open-type="getPhoneNumber" @getphonenumber="getPhoneNumber">
						<!-- <image style="width: 100%;height: 136rpx;" src="https://jiangxiaoxian.0rui.cn/loginBtn.png"></image> -->
						<!-- 授权登录 -->
					</button>
				</view>
			</su-popup>

			<!-- <u-toast ref="uToast"></u-toast> -->

		</view>

		<!-- <s-block v-for="(item, index) in template.data" :key="index" :styles="item.style">
      <s-block-item :type="item.type" :data="item.data" :styles="item.style" />
    </s-block> -->
	</s-layout>
</template>

<script setup>
	import {
		computed,
		ref,
		reactive
	} from 'vue';
	import {
		onShow,
		onPageScroll,
		onPullDownRefresh
	} from '@dcloudio/uni-app';
	import sheep from '@/sheep';
	import userApi from '@/sheep/api/user';
	import third from '@/sheep/api/third';
	import {
		baseUrl,
	} from '@/sheep/config';
	import {
		showShareModal
	} from '@/sheep/hooks/useModal';

	const showLogin = ref(false);
	const iv = ref('');
	const wxCode = ref('');
	const encryptedData = ref('');

	// 隐藏原生tabBar
	uni.hideTabBar({
		fail: () => {},
	});

	const template = computed(() => sheep.$store('app').template.user);
	const isLogin = computed(() => sheep.$store('user').isLogin);
	const userInfo = computed(() => sheep.$store('user').userInfo);
	const authenInfo = computed(() => sheep.$store('user').authenInfo);

	onShow(() => {
		sheep.$store('user').updateUserData();
	});

	onPullDownRefresh(() => {
		sheep.$store('user').updateUserData();
		setTimeout(function() {
			uni.stopPullDownRefresh();
		}, 800);
	});

	onPageScroll(() => {});
	// 打开登录的弹框
	function toLogin() {
		console.log('toLogin', showLogin.value);
		showLogin.value = true;
		console.log('showLogin', showLogin.value);
	}
	// 关闭登录弹框
	function closeLogin() {
		console.log('closeLogin');
		showLogin.value = false;
	}
	// 打开登录的弹框
	function openLogin() {
		console.log('openLogin');
		showLogin.value = true;
	}
	// 使用微信手机号
	async function getPhoneNumber(e) {
		console.log('获取手机号', e, e.detail.errMsg);
		if (e.detail.errMsg !== 'getPhoneNumber:ok') {} else {
			encryptedData.value = e.detail.encryptedData // 用于解密
			iv.value = e.detail.iv // 用于解密
			getCode()
			//    let result = await sheep.$platform.useProvider().bindUserPhoneNumber(e.detail);
			// console.log('result', result);
			//    if (result) {
			//      sheep.$store('user').getInfo();
			//      // closeAuthModal();
			//   closeLogin();
			//    }
		}
	}
	// 获取code和sessionId
	function getCode() {
		uni.login({
			provider: 'weixin',
			success: (res) => {
				wxCode.value = res.code
				// loginResult.value = res
				// getSessionId();
				Login();
			}
		})
	}
	// 登录接口
	function Login() {
		const session_id = uni.getStorageSync('sessionId')
		console.log('session_id', session_id);
		return new Promise(async (resolve, reject) => {
			const {
				code
			} = await third.wechat.wxLogin({
				platform: 'miniProgram',
				payload: encodeURIComponent(
					JSON.stringify({
						sessionId: session_id,
						iv: iv.value,
						encryptedData: encryptedData.value,
						code: wxCode.code,
					}),
				),
			});
			if (code === 1) {
				resolve(true);
				showLogin.value = false;
			}
			resolve(false);
		});
	}
	
	//设置跳转页面
	function toSetting() {
		console.log('isLogin',isLogin.value);
		if(isLogin.value == true){
			uni.navigateTo({
				url:'/pages/user/info'
			})
		}else {
			uni.showToast({
				title: '请先登录',
				icon: 'none'
			})
		}
	}
	
	//页面跳转
	const toPage = (e) => {
		if(isLogin.value == true) {
			uni.navigateTo({
				url: e
			})
		}else{
			uni.showToast({
				title: '请先登录',
				icon: 'none',
				duration: 2000
			})
			
			// setTimeout(function() {
			// 	uni.switchTab({
			// 		url: '/pages/index/user'
			// 	})
			// }, 2000);
			
		}
	}
</script>

<style lang="scss" scoped>
	.pendImg {
		width: 70rpx;
		height: 70rpx;
	}

	.weight4 {
		font-weight: 400;
	}

	.weight8 {
		font-weight: 400;
	}

	.fs-28 {
		font-size: 28rpx;
		line-height: 28rpx;
	}

	.fs-36 {
		font-size: 36rpx;
		line-height: 36rpx;
	}

	.fs-20 {
		font-size: 20rpx;
		line-height: 20rpx;
	}

	.flexD {
		display: flex;
	}

	.gird {
		display: grid;
	}

	.w-100 {
		width: 100%;
	}

	.flex {
		display: flex;
	}

	.flex-start {
		align-items: flex-start;
	}

	.justify-center {
		justify-content: center;
	}

	.align-items {
		align-items: center;
	}

	.flex-column {
		flex-flow: column;
	}

	.justify-start {
		justify-content: start;
	}

	.myBox {
		position: fixed;
		width: 750rpx;
		min-height: 100vh;
		background-color: #F5F5F5;
		background-size: 100%;
		background-repeat: no-repeat;
	}

	.con-center {
		min-height: 80vh;
		padding: 30 30rpx;

		.header {
			width: 93%;
			height: 580rpx;
			padding: 0 30rpx;
			position: relative;
			// top: 100rpx;
			justify-content: space-between;
			align-items: center;
			// margin: 0 auto;
			// padding: 30rpx;
			background-image: url('https://jiangxiaoxian.0rui.cn/topback.png');
			background-size: 100%;

			.hea-left {
				width: 100%;
				height: 140rpx;
				align-items: center;
				position: relative;
				top: 180rpx;

				.avatar {
					width: 140rpx;
					height: 140rpx;
					border-radius: 50%;
				}

				.head-avatar {
					width: 130rpx;
					height: 130rpx;
					margin-right: 30rpx;
				}

				.headAvatar-info {
					display: gird;
					// align-items: center;
					// justify-content: center;

					.info-name {
						display: flex;
						justify-content: flex-start;
						font-weight: 900rpx;
						align-items: center;
						
						.UnAuthenBox {
							margin-left: 10rpx;
							background-color: #EBECEE;
							border-radius: 8rpx;
							width: 80rpx;
							height: 30rpx;
							color: #8D93B3;
							display: flex;
							padding: 5rpx 10rpx 9rpx 10rpx;
							align-items: center;
							justify-content: center;
						}
						

						.authenticationBox {
							margin-left: 10rpx;
							background-color: #333333;
							border-radius: 8rpx;
							width: 80rpx;
							height: 30rpx;
							color: #fecc4e;
							display: flex;
							padding: 5rpx 10rpx 8rpx 10rpx;
							align-items: center;
							justify-content: center;
						}
					}
				}

				.head-signInBox {
					width: 156rpx;
					height: 70rpx;
					position: absolute;
					right: -30rpx;
					display: flex;
					justify-content: center;
					background-color: #fff;
					align-items: center;
					border-radius: 116rpx 0 0 116rpx;

					.signInText {
						font-size: 30rpx;
						font-weight: 800;
						line-height: 28rpx;
						color: #333333;
					}
				}



				.line {
					width: 320rpx;
					height: 1rpx;
					background: #BABDC7;
					border-radius: 0rpx 0rpx 0rpx 0rpx;
					margin: 19rpx 0 7rpx 0;
				}

				.red {
					font-family: PingFang SC, PingFang SC;
					font-weight: 800;
					font-size: 26rpx;
					color: #FF4949;
					line-height: 32rpx;
				}
			}

			.head-secondBox {
				margin-top: 50rpx;
				display: flex;
				justify-content: space-around;
				align-items: center;
				height: 76rpx;
				width: 100%;
				position: relative;
				top: 190rpx;

				.collect {
					display: grid;
					align-items: center;
					text-align: center;
					justify-content: center;
					width: 158rpx;
					height: 100%;

					.collect-num {
						font-size: 34rpx;
						font-weight: 800;
						color: #333333;
						line-height: 28rpx;
					}

					.collect-text {
						margin-top: 20rpx;
						font-size: 28rpx;
						font-weight: 400;
						color: #333333;
						line-height: 28rpx;
					}
				}

				.feetPrint {
					display: grid;
					align-items: center;
					text-align: center;
					justify-content: center;
					width: 188rpx;
					height: 100%;

					.feetPrint-num {
						font-size: 34rpx;
						font-weight: 800;
						color: #333333;
						line-height: 28rpx;
					}

					.feetPrint-text {
						margin-top: 20rpx;
						font-size: 28rpx;
						font-weight: 400;
						color: #333333;
						line-height: 28rpx;
					}
				}
			}

			.line {}

		}

		.myOrder {
			width: 630rpx;
			height: 206rpx;
			margin: 0 auto;
			position: relative;
			bottom: 90rpx;
			background-color: #ffffff;
			border-radius: 18rpx;
			display: block;
			padding: 40rpx 30rpx;

			.myOrder-first {
				width: 100%;
				display: flex;
				justify-content: space-between;
				align-items: center;
			}

			.myOrder-second {
				width: 100%;
				display: flex;
				margin-top: 40rpx;
				justify-content: space-between;

				.pendingBox {
					display: block;
					justify-content: center;
					align-items: center;

				}
			}
		}

		.sixBox {
			width: 630rpx;
			// height: 637rpx;
			display: block;
			background-color: #ffffff;
			border-radius: 18rpx;
			padding: 30rpx;
			margin: 0 auto;
			position: relative;
			bottom: 50rpx;

			.fabuBox {
				display: flex;
				align-items: center;
				justify-content: space-between;
			}

			.line-row {
				width: 630rpx;
				height: 1rpx;
				background: #EEEEEE;
				border-radius: 0rpx 0rpx 0rpx 0rpx;
				margin: 30rpx 0;
			}

		}

	}



	.popup {
		background-image: url('https://jiangxiaoxian.0rui.cn/showLogin.png');
		background-size: 100%;
		width: 600rpx;
		height: 640rpx;
		display: flex;
		padding-bottom: 44rpx;
		flex-direction: column;
		align-items: baseline;
		justify-content: flex-end;
		padding-left: 73rpx;
		padding-right: 73rpx;

		.img_shouquan {
			position: absolute;
			width: 680rpx;
			height: 857rpx;
			left: -25rpx;
			top: -390rpx;
		}


		#txt2 {
			text-align: center;
			margin-top: 200rpx;
			font-size: 28rpx;
			color: #525252;
			margin-bottom: 60rpx;
			position: relative;
			font-weight: 500;
		}

		.texbut {
			width: 507rpx;
			height: 90rpx;
			border-radius: 16rpx;
			// background: #ffbd25;
			background-image: url('https://jiangxiaoxian.0rui.cn/loginBtn.png');
			background-size: 100%;
			font-size: 34rpx;
			color: #3d3d3d;
			font-weight: bold;
			display: flex;
			justify-content: center;
			align-items: center;
			margin: 0 auto;
		}
	}
	
	
</style>