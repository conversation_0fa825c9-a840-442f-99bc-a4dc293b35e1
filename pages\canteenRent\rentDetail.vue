<template>
	<s-layout title="招租详情">
		<view class="container">
			<!-- 顶部banner -->
			<view class="swiper-box">
				<view class="swiper_s">
					<swiper class="swiper_s" :circular="true" :autoplay="true" :indicator-dots="false" :current="currentImgIndex"
						@change="swiperChange" @animationfinish="animationfinish">
						<!-- v-for="(item, index) in swiperList" :key="index" @click="openSwiper(item)"-->
						<swiper-item style="margin: 0 auto;" v-for="(item,index) in imageList" :key="index">
							<view style="position: relative;width: 100%;height: 100%;"
								>
								<image class="swiper-image" :src="item" mode="widthFix"/>
							</view>
							
						</swiper-item>

					</swiper>
					<view class="bannerNum">
						{{currentShowImgIndex}}/{{imagesNum}}
					</view>
				</view>
			</view>

			<!-- 招租基本信息 -->
			<view class="listItem-contents">
				<view class="title2" style="margin-bottom: 20rpx;">{{detailRent.platform_title}}</view>
				<scroll-view class="score-box">
					<view class="fs11 flex cate">
						<view class="cate-e" v-for="(e,i) in detailRent.cate" :key="i">
							<view v-show="e" class="status-tag " style="margin-right: 20rpx;">{{e}}
							</view>
						</view>

						<!-- <text v-show="item.cate[1]"
						class="status-tag" style="margin-right: 20rpx;">{{item.cate[1]}}</text>
					<text v-show="item.cate[2]"
						class="status-tag">{{item.cate[2]}}</text> -->
					</view>
				</scroll-view>
				<view class="cons-third">
					<view style="display: flex;align-items: center;justify-content: flex-start;">
						<image style="width: 36rpx;height: 36rpx;" :src="detailRent.user?.avatar"></image>
						<view class="title1" style="margin-left: 10rpx;">{{detailRent.user?.nickname}}
						</view>
						<view class="title5" style="margin-left: 30rpx;">项目类型： {{detailRent.type_text}}</view>
					</view>

					<view style="display: flex;align-items: center;justify-content: center;">
						<image style="width: 26rpx;height: 20rpx;" src="https://jiangxiaoxian.0rui.cn/eye.png"></image>
						<view class="title3" style="margin-left: 10rpx;">{{detailRent.views}}</view>
					</view>
				</view>




			</view>

			<view class="phoneNumBox">
				<view style="display: flex;justify-content: flex-start;align-items: center;">
					<image style="width: 32rpx;height: 32rpx;" src="https://jiangxiaoxian.0rui.cn/rentDetailTime.png">
					</image>
					<view class="title4" style="margin-left: 20rpx;">{{detailRent.release_time_text}}</view>
				</view>
				<view class="line-row" style="margin: 40rpx 0;"></view>
				<view style="display: flex;justify-content: flex-start;align-items: center;">
					<image style="width: 32rpx;height: 32rpx;" src="https://jiangxiaoxian.0rui.cn/rentAddress.png">
					</image>
					<view class="title4" style="margin-left: 20rpx;">{{detailRent.address_detail}}</view>
				</view>
			</view>

			<view class="description">
				<view class="desTi">
					<view style="width: 6rpx;height: 36rpx;background-color: #fcc74e;margin-right: 20rpx;"></view>
					<view class="cs bold" style="font-size: 36rpx;line-height: 38rpx;">详细介绍</view>
				</view>
				<view class="desCon">
					{{detailRent.content}}
				</view>
			</view>

			<view style="width: 100%;height: 180rpx;"></view>

			<!-- 底部按钮 -->
			<view class="footer-box">
				<view class="icon-box flex ml40">
					<view @click="backHome">
						<image class="btnIcon" src="https://jiangxiaoxian.0rui.cn/backHome.png" mode=""></image>
						<view class="btnText" style="margin-top: 10rpx;">首页</view>
					</view>
					<view style="margin-left: 40rpx;" @click="showShareModal">
						<image class="btnIcon" src="https://jiangxiaoxian.0rui.cn/rentShare.png" mode=""></image>
						<view class="btnText" style="margin-top: 10rpx;">分享</view>
					</view>
					<view style="margin-left: 40rpx;" @click="handleColect">
						<image v-if="detailRent.is_collect == 0" class="btnIcon"
							src="https://jiangxiaoxian.0rui.cn/unCollect.png" mode=""></image>
						<image v-else class="btnIcon" src="https://jiangxiaoxian.0rui.cn/collected.png" mode=""></image>
						<view class="btnText" style="margin-top: 10rpx;">收藏</view>
					</view>
					<view style="margin-left: 40rpx;" @click="handleService()">
						<image class="btnIcon" src="https://jiangxiaoxian.0rui.cn/service.png" mode=""></image>
						<view class="btnText" style="margin-top: 10rpx;">客服</view>
					</view>
				</view>
				<view class="button-box" @click="handleJuage">立即联系
				</view>
			</view>

		</view>

		<su-popup :show="showContacted == true" type="center" round="10" :isMaskClick="false">
			<view class="popupContacted">
				<view class="contactedBox">
					<image style="width: 120rpx;height: 120rpx;position: relative;left: 54rpx;"
						src="https://jiangxiaoxian.0rui.cn/rentContact1.png"></image>
					<view
						style="font-size: 36rpx;line-height: 50rpx;font-weight: 900;color: #3d3d3d;position: relative;left: 44rpx;">
						确认拨号</view>
					<view style="font-size: 30rpx;line-height: 44rpx;font-weight: 400;color: #3d3d3d;">
						剩余联系次数：{{detailRent.all_mobile_num}}</view>
				</view>
				<view class="contactBtn">
					<view class="tactBtnBox" style="color: #999999;" @click="showContacted = false">取消</view>
					<view class="tactBtnBox" style="color: #333333;" @click="handleTell">确定</view>
				</view>
			</view>
		</su-popup>

		<su-popup :show="showContactNo == true" type="center" round="10" :isMaskClick="false">
			<view class="popupContactNo">
				<view class="contactNoBox">
					<image style="width: 120rpx;height: 120rpx;position: relative;left: 190rpx;"
						src="https://jiangxiaoxian.0rui.cn/rentContact2.png"></image>
					<view
						style="font-size: 36rpx;line-height: 50rpx;font-weight: 900;color: #3d3d3d;position: relative;left: 180rpx;">
						非常抱歉</view>
					<view style="font-size: 30rpx;line-height: 44rpx;font-weight: 400;color: #3d3d3d;">你的联系次数不足请前往积分商城兑换
					</view>
				</view>
				<view class="contactBtn">
					<view class="tactBtnBox" style="color: #999999;" @click="showContactNo = false">取消</view>
					<view class="tactBtnBox" style="color: #333333;" @click="gotoExchange">去兑换</view>
				</view>
			</view>
		</su-popup>

	</s-layout>
</template>

<script setup>
	import {
		computed,
		ref,
		reactive,
	} from 'vue';
	import {
		onLoad,
		onShow,
		onPageScroll,
		onPullDownRefresh,
		onReachBottom
	} from '@dcloudio/uni-app';
	import {
		consignee,
		mobile,
		address,
		region
	} from '@/sheep/validate/form';
	import rent from '../../sheep/api/rent';
	import sheep from '@/sheep';
	import {
		showShareModal
	} from '@/sheep/hooks/useModal';

	const rentId = ref('');
	const detailRent = ref('')
	const imagesNum = ref(0);
	const currentImgIndex = ref(0)
	const currentShowImgIndex = ref(1)
	const showContacted = ref(false)
	const showContactNo = ref(false)
	onLoad((options) => {
		console.log('onload', options.id);
		rentId.value = options.id
		// if (options.id) {
		// 	getDetail();
		// }
	})

	onShow(() => {
		if (rentId.value) {
			getDetail();
		}
	})
	
	const imageList = ref([])
	async function getDetail() {
		const res = await sheep.$api.rent.rentInfo(rentId.value)
		console.log('获取招租详情', res);
		if (res.code == 1) {
			detailRent.value = res.data.detail
			detailRent.value.release_time_text = detailRent.value.release_time_text.substring(0, 10);

			imageList.value = res.data.detail.images
			console.log('imageList:', imageList.value);
			imagesNum.value = imageList.value.length
			console.log('imagesNum:', imagesNum.value);
		} else {
			uni.showToast({
				title: res.msg,
				icon: 'none'
			})
		}
	}
	
	function swiperChange(e) {
		console.log('切换轮播图',e.detail.current);
		if(e.detail.current<imagesNum.value) {
			// if(e.detail.current == 0){
			// 	currentImgIndex.value = imagesNum.value
			// }else {
			// 	currentImgIndex.value = e.detail.current;
			// }
			currentImgIndex.value = e.detail.current;
			currentShowImgIndex.value = e.detail.current + 1;
		}
		// if(e.detail.source =='autoplay' || e.detail.source == 'touch' || e.detail.source === ''){
		// 	currentImgIndex.value = e.detail.current + 1;
		// }else {
		// 	// currentImgIndex.value = e.detail.current + 1; // 无条件更新
		// }
	}
	
	function animationfinish(e) {
	 console.log('animationfinish',e);
	};

	//返回首页
	function backHome() {
		uni.switchTab({
			url: '/pages/index/index'
		})
	}

	//页面跳转
	const toPage = (e) => {
		uni.navigateTo({
			url: e
		})
	}

	//收藏
	function handleColect() {
		console.log('收藏', detailRent.value.is_collect);
		if (detailRent.value.is_collect == 1) {
			detailRent.value.is_collect = 0
			const data = {
				id: detailRent.value.id,
				is_collect: detailRent.value.is_collect
			}
			sheep.$api.rent.rentCollect(data).then((res) => {
				if (res.code == 1) {
					uni.showToast({
						title: '已取消收藏',
						icon: 'none',
						duration: 2000
					})
					setTimeout(() => {
						getDetail()
					}, 1000)
					// getDetail()
				}
			})


		} else {
			detailRent.value.is_collect = 1
			const data1 = {
				id: detailRent.value.id,
				is_collect: detailRent.value.is_collect
			}
			sheep.$api.rent.rentCollect(data1).then((res) => {
				if (res.code == 1) {
					uni.showToast({
						title: '已收藏',
						icon: 'none',
						duration: 2000
					})
					setTimeout(() => {
						getDetail()
					}, 1000)
					// getDetail()
				}
			})

		}


	}

	//是否拨打电话的次数
	function handleJuage() {
		console.log('拨打电话次数:', detailRent.value.all_mobile_num);
		if (detailRent.value.all_mobile_num > 0) {
			showContacted.value = true
		} else {
			showContactNo.value = true
		}
	}
	const phoneTell = ref()
	//确定拨打电话
	async function handleTell() {
		const res = await sheep.$api.rent.getPhoneNum({
			id: rentId.value
		})
		console.log('获取电话', res);
		if (res.code == 1) {
			phoneTell.value = res.data.contact_number
			showContacted.value = false
			wx.makePhoneCall({
				phoneNumber: phoneTell.value,
				success: function() {
					console.log("拨打电话成功")

				},
				fail: function(err) {
					console.log("拨打电话失败", err)
				}
			})
		}
	}
	//去兑换拨号次数
	function gotoExchange() {
		showContactNo.value = false
		uni.navigateTo({
			url: '/pages/app/score-shop'
		})
	}

	//联系平台客服
	function handleService() {
		console.log('联系平台客服',detailRent.value.platform_contact_number);
		wx.makePhoneCall({
			phoneNumber: detailRent.value.platform_contact_number,
			success: function() {
				console.log("拨打电话成功")

			},
			fail: function(err) {
				console.log("拨打电话失败", err)
			}
		})
	}
</script>

<style lang="scss" scoped>
	.btnIcon {
		width: 40rpx;
		height: 40rpx;
	}

	.btnText {
		font-size: 20rpx;
		font-weight: 400;
		line-height: 20rpx;
		color: #3d3d3d;
	}

	.title1 {
		font-size: 24rpx;
		font-weight: 400;
		color: #9c9c9c;
		line-height: 26rpx;
	}

	.title2 {
		color: #3d3d3d;
		font-size: 30rpx;
		font-weight: 800;
		line-height: 42rpx;
	}

	.title3 {
		font-size: 24rpx;
		font-weight: 400;
		color: #999999;
		line-height: 26rpx;
	}

	.title4 {
		font-size: 26rpx;
		font-weight: 400;
		color: #9b9b9b;
		line-height: 36rpx;
	}

	.title5 {
		font-size: 24rpx;
		font-weight: 400;
		color: #333333;
		line-height: 26rpx;
	}

	.flex {
		display: flex;
	}

	.flex-start {
		align-items: flex-start;
	}

	.justify-center {
		justify-content: center;
	}

	.align-items {
		align-items: center;
	}

	.flex-column {
		flex-flow: column;
	}

	.fs24 {
		font-size: 24rpx;
	}

	.Regular {
		font-weight: 400;
	}

	.bold {
		font-weight: bold;
	}

	.c3 {
		color: #333333;
	}

	.c9 {
		color: #999999;
	}

	.container {
		width: 100%;

		.swiper-box {
			width: 100%;
			height: 750rpx;
			// height: auto;
			position: relative;

			.swiper_s {
				width: 100%;
				 height: 750rpx;
				// height: 100%;
				// display: grid;
				// justify-content: center;

				.bannerNum {
					z-index: 100;
					width: 85rpx;
					height: 40rpx;
					background-color: rgba(0, 0, 0, 0.5);
					color: #ffffff;
					font-weight: 400;
					font-size: 26rpx;
					line-height: 20rpx;
					border-radius: 30rpx;
					position: relative;
					left: 628rpx;
					bottom: 64rpx;
					display: flex;
					justify-content: center;
					align-items: center;
				}

				.swiper-image {
					width: 100%;
					height: 100%;
					// border-radius: 36rpx;
					object-fit: contain;
				}
			}

		}

		.listItem-contents {
			background-color: #ffffff;
			padding: 30rpx;
			// margin-left: 20rpx;
			// padding: 30rpx;

			.score-box {
				display: flex;

				.cate {
					display: flex;
					justify-content: flex-start;
					align-items: center;

					.cate-e {

						.status-tag {
							padding: 5rpx 15rpx;
							border-radius: 5rpx;
							font-size: 22rpx;
							font-weight: 400;
							line-height: 22rpx;
							background-color: rgba(247, 97, 0, 0.2);

							// opacity: 0.2;
							color: #F76100;
							//flex-shrink: 0
						}
					}
				}



			}



			.cons-third {
				margin-top: 20rpx;
				// padding: 30rpx;
				display: flex;
				justify-content: space-between;
				align-items: center;
			}



		}

		.phoneNumBox {
			margin-top: 10rpx;
			background-color: #ffffff;
			padding: 40rpx 30rpx;

			.line-row {
				width: 100%;
				height: 1rpx;
				background: #F0F0F0;
				border-radius: 0rpx 0rpx 0rpx 0rpx;
			}
		}

		.description {
			background-color: #ffffff;
			padding: 40rpx 30rpx;
			margin-top: 10rpx;

			.desTi {
				display: flex;
				justify-content: flex-start;
				align-items: center;
			}

			.desCon {
				margin-top: 30rpx;
				color: #3d3d3d;
				font-weight: 400;
				line-height: 42rpx;
				font-size: 28rpx;
			}

		}

		.footer-box {
			width: 100%;
			height: 140rpx;
			padding-bottom: 0rpx;
			box-sizing: border-box;
			position: fixed;
			bottom: 5rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
			// box-shadow: 0 -4rpx 6rpx 6rpx #f6f6f6;
			background-color: #fff;
			// padding-left: 30rpx;

			.icon-box {
				width: 50%;
				height: 110rpx;
				display: flex;
				justify-content: space-around;
				align-items: center;
				padding: 14rpx 30rpx;
			}

			.button-box {
				background-color: #fcc74e;
				color: #333333;
				width: 50%;
				height: 110rpx;
				display: flex;
				align-items: center;
				justify-content: center;
			}

		}

	}

	.popupContacted {
		width: 660rpx;
		height: 477rpx;
		background-color: #fff;
		display: grid;
		border-radius: 18rpx;

		.contactedBox {
			width: 660rpx;
			height: 325rpx;
			padding: 30rpx 0;
			// margin-top: 40rpx;
			display: grid;
			justify-content: center;
			align-items: center;
		}

		.contactBtn {
			width: 660rpx;
			height: 92rpx;
			border-top: 1rpx solid #eeeeee;
			display: flex;
			border-radius: 0 0 18rpx 18rpx;
			// margin-top: 40rpx;

			.tactBtnBox {
				width: 330rpx;
				height: 92rpx;
				border-right: 1rpx solid #eeeeee;
				display: flex;
				align-items: center;
				justify-content: center;
			}
		}

	}

	.popupContactNo {
		width: 660rpx;
		height: 477rpx;
		background-color: #fff;
		display: grid;
		border-radius: 18rpx;

		.contactNoBox {
			width: 660rpx;
			height: 325rpx;
			padding: 30rpx 0;
			display: grid;
			justify-content: center;
			align-items: center;
		}

		.contactBtn {
			width: 660rpx;
			height: 92rpx;
			border-top: 1rpx solid #eeeeee;
			display: flex;
			border-radius: 0 0 18rpx 18rpx;

			.tactBtnBox {
				width: 330rpx;
				height: 92rpx;
				border-right: 1rpx solid #eeeeee;
				display: flex;
				align-items: center;
				justify-content: center;
			}
		}
	}
</style>